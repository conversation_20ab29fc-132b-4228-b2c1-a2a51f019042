"""
Neura Voice P2P Lending Agent

This module provides a voice-enabled agent for P2P lending queries using
LlamaIndex for RAG (Retrieval-Augmented Generation) and vector search.
"""

# Standard library imports
import asyncio
import logging
import os
import sys
import time
from typing import Annotated, Optional

# Third-party imports
from dotenv import load_dotenv
import json
from datetime import datetime
import requests

from prompt import get_enhanced_system_prompt

from linkup import LinkupClient
from clients.retrive import VectorDBLoader

from livekit.plugins.turn_detector.multilingual import MultilingualModel
from livekit.agents import (
    Agent, AgentSession, JobContext, WorkerOptions, cli, UserStateChangedEvent,
    metrics, MetricsCollectedEvent, JobProcess, AudioConfig, BackgroundAudioPlayer,
    BuiltinAudioClip, vad
)
from livekit.plugins import openai, noise_cancellation, google, silero
from livekit.agents.llm import ChatContext, function_tool
from livekit.agents.voice.room_io import RoomInputOptions, RoomOutputOptions
from livekit.agents.metrics import (
    LLMMetrics, STTMetrics, TTSMetrics, EOUMetrics, VADMetrics
)
from livekit.agents import ConversationItemAddedEvent

# Load environment variables
load_dotenv()

# Constants
LOGS_DIR = "logs"
METRICS_DIR = "metrics"
PERSIST_DIR = "./neura-voice-p2p-engine-storage"
DOCS_DIR = "docs"
QUESTION_COL = "question"
ANSWER_COL = "answer"
BATCH_SIZE = 100
MEMORY_WARN_MB = 1024

# Logging setup
os.makedirs(LOGS_DIR, exist_ok=True)
os.makedirs(METRICS_DIR, exist_ok=True)

class ImportantLogsFilter(logging.Filter):
    """Filters out verbose logs from third-party libraries."""
    def filter(self, record):
        if record.levelno >= logging.WARNING:
            return True
        if record.name == '__main__' or 'main_agent' in record.name:
            return True
        if any(name in record.name for name in [
            'httpcore', 'httpx', 'rustls', 'tungstenite', 'livekit_api',
            'openai._base_client', 'anyio', 'urllib3'
        ]):
            return False
        if 'Added VAD metrics' in record.getMessage():
            if not hasattr(self, '_vad_counter'):
                self._vad_counter = 0
            self._vad_counter += 1
            return self._vad_counter % 10 == 0
        if 'json_data' in record.getMessage() and len(record.getMessage()) > 1000:
            return False
        return True

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
root_logger = logging.getLogger()
root_logger.addFilter(ImportantLogsFilter())
logger = logging.getLogger(__name__)

_vector_db_loader: Optional[VectorDBLoader] = None
_metrics_collector = None

class SessionMetricsCollector:
    """
    Collects and manages metrics for a single session, structuring them
    into conversational turns with corrected finalization logic.
    """
    def __init__(self, session_name: str):
        self.session_name = session_name
        self.session_start_time = datetime.now()
        self.metrics = {
            "session_info": {
                "session_name": session_name,
                "start_time": self.session_start_time.isoformat(),
                "end_time": None,
                "duration_seconds": None,
            },
            "structured_conversation": [],
            "usage_summary": None,
        }
        self._current_turn = None

    def _get_or_create_turn(self):
        """Gets or creates the current turn structure."""
        if self._current_turn is None:
            self._current_turn = {
                "user_turn": {"timestamp": None, "content": "", "metrics": {"stt": [], "vad": [], "eou": []}},
                "assistant_turn": {"timestamp": None, "content": "", "function_calls": [], "metrics": {"llm": [], "tts": []}},
            }
        return self._current_turn

    def _get_user_turn(self):
        """
        Gets the user_turn. A new user action finalizes the previous turn if it was complete.
        """
        # A new user utterance signals the previous turn is complete.
        if self._current_turn and self._current_turn['assistant_turn'].get('content'):
            # Only save turns that have meaningful content
            if self._current_turn['user_turn'].get('content') and self._current_turn['assistant_turn'].get('content'):
                self.metrics["structured_conversation"].append(self._current_turn)
            self._current_turn = None
        return self._get_or_create_turn()['user_turn']

    def _get_assistant_turn(self):
        """Gets the assistant_turn. Assistant actions always belong to the current turn."""
        return self._get_or_create_turn()['assistant_turn']
        
    def add_llm_metrics(self, metrics: LLMMetrics):
        try:
            assistant_turn = self._get_assistant_turn()
            metrics_data = {
                "type": metrics.type, "label": metrics.label, "request_id": metrics.request_id,
                "timestamp": metrics.timestamp if isinstance(metrics.timestamp, (int, float)) else metrics.timestamp.isoformat() if metrics.timestamp else None,
                "duration": metrics.duration, "ttft": metrics.ttft, "cancelled": metrics.cancelled,
                "completion_tokens": metrics.completion_tokens, "prompt_tokens": metrics.prompt_tokens,
                "total_tokens": metrics.total_tokens, "tokens_per_second": metrics.tokens_per_second
            }
            assistant_turn["metrics"]["llm"].append(metrics_data)
        except Exception as e:
            logger.error(f"❌ ERROR adding LLM metrics: {e}", exc_info=True)

    def add_stt_metrics(self, metrics: STTMetrics):
        try:
            user_turn = self._get_user_turn()
            metrics_data = {
                "type": metrics.type, "label": metrics.label, "request_id": metrics.request_id,
                "timestamp": metrics.timestamp if isinstance(metrics.timestamp, (int, float)) else metrics.timestamp.isoformat() if metrics.timestamp else None,
                "duration": metrics.duration, "speech_id": metrics.speech_id,
                "error": str(metrics.error) if metrics.error else None,
                "streamed": metrics.streamed, "audio_duration": metrics.audio_duration
            }
            user_turn["metrics"]["stt"].append(metrics_data)
        except Exception as e:
            logger.error(f"❌ ERROR adding STT metrics: {e}", exc_info=True)

    def add_tts_metrics(self, metrics: TTSMetrics):
        try:
            assistant_turn = self._get_assistant_turn()
            metrics_data = {
                "type": metrics.type, "label": metrics.label, "request_id": metrics.request_id,
                "timestamp": metrics.timestamp if isinstance(metrics.timestamp, (int, float)) else metrics.timestamp.isoformat() if metrics.timestamp else None,
                "ttfb": metrics.ttfb, "duration": metrics.duration, "audio_duration": metrics.audio_duration,
                "cancelled": metrics.cancelled, "characters_count": metrics.characters_count,
                "streamed": metrics.streamed, "speech_id": metrics.speech_id,
                "error": str(getattr(metrics, 'error', None)) if getattr(metrics, 'error', None) else None
            }
            assistant_turn["metrics"]["tts"].append(metrics_data)
        except Exception as e:
            logger.error(f"❌ ERROR adding TTS metrics: {e}", exc_info=True)

    def add_eou_metrics(self, metrics: EOUMetrics):
        try:
            user_turn = self._get_user_turn()
            metrics_data = {
                "type": "eou_metrics", "label": "eou_metrics",
                "timestamp": metrics.timestamp if isinstance(metrics.timestamp, (int, float)) else metrics.timestamp.isoformat() if metrics.timestamp else None,
                "end_of_utterance_delay": getattr(metrics, 'end_of_utterance_delay', None),
                "transcription_delay": getattr(metrics, 'transcription_delay', None), "speech_id": metrics.speech_id,
                "error": str(getattr(metrics, 'error', None)) if getattr(metrics, 'error', None) else None
            }
            user_turn["metrics"]["eou"].append(metrics_data)
        except Exception as e:
            logger.error(f"❌ ERROR adding EOU metrics: {e}", exc_info=True)

    def add_vad_metrics(self, event):
        try:
            # Only collect meaningful VAD metrics to avoid spam
            speech_id = getattr(event, 'speech_id', None)
            idle_time = getattr(event, 'idle_time', 0)
            
            # Only collect VAD metrics when there's actual speech activity
            if not speech_id:
                return
                
            user_turn = self._get_user_turn()
            metrics_data = {
                "type": getattr(event, 'type', 'vad_metrics'),
                "timestamp": event.timestamp if isinstance(event.timestamp, (int, float)) else event.timestamp.isoformat() if hasattr(event, 'timestamp') and event.timestamp else None,
                "idle_time": idle_time,
                "inference_duration_total": getattr(event, 'inference_duration_total', None),
                "inference_count": getattr(event, 'inference_count', None),
                "speech_id": speech_id,
                "error": str(getattr(event, 'error', None)) if getattr(event, 'error', None) else None
            }
            user_turn["metrics"]["vad"].append(metrics_data)
        except Exception as e:
            logger.error(f"❌ ERROR adding VAD metrics: {e}", exc_info=True)

    def add_conversation_entry(self, role: str, content: str):
        """Add a conversation entry to the appropriate turn."""
        if not content: return
        timestamp = datetime.now().isoformat()
        
        if role == "user":
            user_turn = self._get_user_turn()
            if not user_turn["timestamp"]: user_turn["timestamp"] = timestamp
            user_turn["content"] = content.strip()
        elif role == "assistant":
            assistant_turn = self._get_assistant_turn()
            if not assistant_turn["timestamp"]: assistant_turn["timestamp"] = timestamp
            assistant_turn["content"] = content.strip()
        
        logger.info(f"Conversation Entry: {role.title()} - {content[:80].strip()}...")

    def add_function_call(self, function_name: str, args: dict, result: str = None):
        assistant_turn = self._get_assistant_turn()
        entry = {
            "timestamp": datetime.now().isoformat(), "function_name": function_name, "arguments": args, "result": result,
        }
        assistant_turn["function_calls"].append(entry)
        logger.info(f"Function Call: {function_name}")

    def set_usage_summary(self, summary: str):
        self.metrics["usage_summary"] = summary


    def finalize_session(self):
        end_time = datetime.now()
        self.metrics["session_info"]["end_time"] = end_time.isoformat()
        self.metrics["session_info"]["duration_seconds"] = (end_time - self.session_start_time).total_seconds()

        # Final check to save the very last turn of the conversation
        if self._current_turn:
            user_content = self._current_turn.get("user_turn", {}).get("content", "")
            assistant_content = self._current_turn.get("assistant_turn", {}).get("content", "")
            # Only save complete turns that have both user and assistant content
            if user_content and assistant_content:
                self.metrics["structured_conversation"].append(self._current_turn)
            self._current_turn = None

    def save_to_json(self):
        try:
            self.finalize_session()
            filename = f"{self.session_name}_metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            filepath = os.path.join(METRICS_DIR, filename)
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.metrics, f, indent=2, ensure_ascii=False)

            logger.info(f"✅ Session metrics saved to: {filepath}")
            turn_count = len(self.metrics.get('structured_conversation', []))
            logger.info(f"📊 Session summary - Turns captured: {turn_count}")

        except Exception as e:
            logger.error(f"❌ Failed to save metrics to JSON: {e}", exc_info=True)


async def get_vector_db_loader() -> VectorDBLoader:
    global _vector_db_loader
    if _vector_db_loader is None:
        loop = asyncio.get_running_loop()
        _vector_db_loader = await loop.run_in_executor(None, VectorDBLoader)
    return _vector_db_loader

@function_tool
async def search_knowledge_base(query: Annotated[str, "Vector search query"]) -> str:
    global _metrics_collector
    loader = await get_vector_db_loader()
    results = await loader.search(query, top_k=2)
    result = "\n\n".join([f"{idx}. {item.get('formatted_text', '').strip()}" for idx, item in enumerate(results, start=1)]) \
             if results else "I'm sorry, I couldn't find relevant information in the knowledge base."
    if _metrics_collector:
        _metrics_collector.add_function_call("search_knowledge_base", {"query": query}, result[:200] + "...")
    return result

@function_tool
async def web_search(query: Annotated[str, "Web search query"]) -> str:
    global _metrics_collector
    try:
        client = LinkupClient(api_key=os.getenv('LINKUP_API_KEY'))
        response = await asyncio.get_running_loop().run_in_executor(None, 
            lambda: client.search(query=query, depth="standard", output_type="sourcedAnswer"))
        result = response.answer if hasattr(response, 'answer') and response.answer else "No direct answer found."
    except Exception as e:
        logger.error(f"Web search error: {e}")
        result = "Web search error. Please try again later."
    if _metrics_collector:
        _metrics_collector.add_function_call("web_search", {"query": query}, result[:200] + "...")
    return result

_report_requested_global = False

@function_tool
async def get_report_with_verification(last_four_digits: Annotated[str, "Last 4 digits of mobile number"]) -> str:
    global _report_requested_global, _metrics_collector
    if _metrics_collector:
        _metrics_collector.add_function_call("get_report_with_verification", {"last_four_digits": last_four_digits}, "Attempting verification...")
    if _report_requested_global:
        return "Your statement for this session has already been generated."
    
    def convert_and_extract(text):
        hindi_map = {'०':'0', '१':'1', '२':'2', '३':'3', '४':'4', '५':'5', '६':'6', '७':'7', '८':'8', '९':'9'}
        for h, e in hindi_map.items(): text = text.replace(h, e)
        return ''.join(filter(str.isdigit, text))

    try:
        input_digits = convert_and_extract(last_four_digits)
        if len(input_digits) < 4:
            return "Please provide the complete four digits of your registered mobile number."
        user_last_four = input_digits[-4:]
        registered_numbers = ["8308104566", "9773389667", "8586049983", "9765980646"]
        matched_number = next((n for n in registered_numbers if n.endswith(user_last_four)), None)
        if not matched_number:
            return f"The last 4 digits you provided ({user_last_four}) do not match our records."

        _report_requested_global = True
        token_url = f"https://qa-investor-api.lendenclub.com/api/ims/common/v1/get-user-token?mobile_number={matched_number}"
        token_res = requests.get(token_url)
        token_res.raise_for_status()
        token_data = token_res.json().get('data', {})
        access_token, ldc_code = token_data.get('access_token'), token_data.get('ldc_code')
        if not access_token or not ldc_code:
            _report_requested_global = False
            return "Could not verify your account details."

        headers = {"Authorization": f"Token {access_token}", "x-ldc-key": ldc_code}
        report_url = "https://qa-investor-api.lendenclub.com/api/ims/retail-investor/v5/web/statement-type"
        params = {"statement_type": "FMPP_ACCOUNT_STATEMENT", "months": "12", "send_email": "true"}
        email_res = requests.get(report_url, headers=headers, params=params)
        
        if email_res.status_code == 200:
            return f"✅ Your 12-month FMPP account statement has been generated and sent to your registered email address for number ending in {user_last_four}."
        else:
            _report_requested_global = False
            return f"A technical issue occurred while generating the report. Response code: {email_res.status_code}."

    except Exception as e:
        logger.error(f"❌ Unexpected error in get_report: {e}", exc_info=True)
        _report_requested_global = False
        return "An unexpected technical error occurred. Please contact customer support."

class NeuraVoiceAgent(Agent):
    def __init__(self, chat_ctx: ChatContext, metrics_collector: SessionMetricsCollector) -> None:
        super().__init__(
            instructions=get_enhanced_system_prompt(), chat_ctx=chat_ctx,
            tools=[search_knowledge_base, web_search, get_report_with_verification]
        )
        self.metrics_collector = metrics_collector
    async def on_enter(self) -> None:
        try:
            await self.session.generate_reply(instructions="Greet the user warmly and ask how you can help.")
        except Exception as e:
            logger.error(f"Error generating greeting: {e}")

def prewarm(proc: JobProcess):
    proc.userdata["vad"] = silero.VAD.load()

async def entrypoint(ctx: JobContext):
    logger.info("🚀 Starting agent entrypoint...")
    try:
        session_name = ctx.room.name or f"session_{int(time.time())}"
        metrics_collector = SessionMetricsCollector(session_name)
        log_file_path = os.path.join(LOGS_DIR, f"{session_name}.log")
        file_handler = logging.FileHandler(log_file_path)
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(name)s - %(message)s'))
        file_handler.addFilter(ImportantLogsFilter())
        root_logger.addHandler(file_handler)
        logger.info(f"📊 Collector created for session: {session_name}, logging to {log_file_path}")
        
        global _metrics_collector
        _metrics_collector = metrics_collector

        stt = openai.STT(model='gpt-4o-mini-transcribe')
        llm = openai.LLM(model='gpt-4o-mini', temperature=0.6)
        tts = google.TTS(language='hi-IN', gender='female', voice_name='hi-IN-Chirp3-HD-Laomedeia',
                         sample_rate=24000, speaking_rate=0.95, use_streaming=True, location='asia-southeast1',
                         credentials_file=os.getenv('GOOGLE_APPLICATION_CREDENTIALS'))
        vad_component = ctx.proc.userdata["vad"]

        def create_metric_handler(metric_adder):
            def wrapper(metrics):
                try: asyncio.create_task(asyncio.to_thread(metric_adder, metrics))
                except Exception as e: logger.error(f"❌ Metrics wrapper error: {e}", exc_info=True)
            return wrapper

        llm.on("metrics_collected", create_metric_handler(metrics_collector.add_llm_metrics))
        stt.on("metrics_collected", create_metric_handler(metrics_collector.add_stt_metrics))
        stt.on("eou_metrics_collected", create_metric_handler(metrics_collector.add_eou_metrics))
        tts.on("metrics_collected", create_metric_handler(metrics_collector.add_tts_metrics))
        vad_component.on("metrics_collected", create_metric_handler(metrics_collector.add_vad_metrics))

        session = AgentSession(stt=stt, vad=vad_component, turn_detection=MultilingualModel(), llm=llm, tts=tts,
                               allow_interruptions=True, user_away_timeout=30.0)

        # THIS IS THE ROBUST CONTENT EXTRACTION HELPER
        def _extract_text_content(content):
            if isinstance(content, str): return content.strip()
            if isinstance(content, list):
                return "".join(str(part.get("text", part) if isinstance(part, dict)
                               else getattr(part, "text", str(part))) for part in content).strip()
            return str(content).strip() if content else ""

        @session.on("conversation_item_added")
        def _on_conversation_item_added(event: ConversationItemAddedEvent):
            try:
                item = getattr(event, "item", None)
                if not item: return
                role = getattr(item, "role", "")
                content_parts = getattr(item, "content", [])
                content = _extract_text_content(content_parts)
                if content and role in ["user", "assistant"]:
                    metrics_collector.add_conversation_entry(role, content)
            except Exception as e:
                logger.error(f"Conversation logging error: {e}", exc_info=True)
        
        usage_collector = metrics.UsageCollector()
        @session.on("metrics_collected")
        def _on_metrics_collected(event: MetricsCollectedEvent):
            usage_collector.collect(event.metrics)

        async def shutdown_handler():
            summary = usage_collector.get_summary()
            metrics_collector.set_usage_summary(str(summary))
            await asyncio.to_thread(metrics_collector.save_to_json)

        ctx.add_shutdown_callback(shutdown_handler)

        bot = NeuraVoiceAgent(chat_ctx=ChatContext(), metrics_collector=metrics_collector)
        asyncio.create_task(get_vector_db_loader())
        await session.start(agent=bot, room=ctx.room, room_input_options=RoomInputOptions(noise_cancellation=noise_cancellation.BVC()),
                            room_output_options=RoomOutputOptions(transcription_enabled=True))
        
        background_audio = BackgroundAudioPlayer(ambient_sound=AudioConfig(BuiltinAudioClip.OFFICE_AMBIENCE, volume=0.8))
        await background_audio.start(room=ctx.room, agent_session=session)
        logger.info("✅ Agent session started.")

    except asyncio.CancelledError:
        logger.info("Agent session cancelled.")
    except Exception as e:
        logger.critical(f"❌ CRITICAL ERROR in entrypoint: {e}", exc_info=True)
        if 'metrics_collector' in locals(): await asyncio.to_thread(metrics_collector.save_to_json)
        raise

if __name__ == "__main__":
    cli.run_app(
        WorkerOptions(entrypoint_fnc=entrypoint, job_memory_warn_mb=max(MEMORY_WARN_MB, 1024),
                      prewarm_fnc=prewarm, job_memory_limit_mb=0, initialize_process_timeout=25)
    )