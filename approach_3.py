
"""
Neura Voice P2P Lending Agent

This module provides a voice-enabled agent for P2P lending queries using
LlamaIndex for RAG (Retrieval-Augmented Generation) and vector search.
"""

# Standard library imports
import asyncio
import logging
import os
import sys
import time
from typing import Annotated, Optional

# Third-party imports
from dotenv import load_dotenv
import json
from datetime import datetime
import requests

from prompt import get_enhanced_system_prompt

from linkup import LinkupClient
from clients.retrive import VectorDBLoader

from livekit.plugins.turn_detector.multilingual import MultilingualModel
from livekit.agents import (
    Agent, AgentSession, JobContext, WorkerOptions, cli, UserStateChangedEvent,
    metrics, MetricsCollectedEvent, JobProcess, AudioConfig, BackgroundAudioPlayer,
    BuiltinAudioClip, vad
)
from livekit.plugins import openai, noise_cancellation, google, silero
from livekit.agents.llm import ChatContext, function_tool
from livekit.agents.voice.room_io import RoomInputOptions, RoomOutputOptions
from livekit.agents.metrics import (
    LLMMetrics, STTMetrics, TTSMetrics, EOUMetrics, VADMetrics
)
from livekit.agents import ConversationItemAddedEvent

# Load environment variables
load_dotenv()

# Constants
LOGS_DIR = "logs"
METRICS_DIR = "metrics"
PERSIST_DIR = "./neura-voice-p2p-engine-storage"
DOCS_DIR = "docs"
QUESTION_COL = "question"
ANSWER_COL = "answer"
BATCH_SIZE = 100
MEMORY_WARN_MB = 1024

# Logging setup
os.makedirs(LOGS_DIR, exist_ok=True)
os.makedirs(METRICS_DIR, exist_ok=True)

# Create a custom filter to reduce verbose external library logs
class ImportantLogsFilter(logging.Filter):
    """Filters out verbose logs from third-party libraries."""
    def filter(self, record):
        if record.levelno >= logging.WARNING:
            return True
        if record.name == '__main__' or 'main_agent' in record.name:
            return True
        if any(name in record.name for name in [
            'httpcore', 'httpx', 'rustls', 'tungstenite', 'livekit_api',
            'openai._base_client', 'anyio', 'urllib3'
        ]):
            return False
        if 'Added VAD metrics' in record.getMessage():
            if not hasattr(self, '_vad_counter'):
                self._vad_counter = 0
            self._vad_counter += 1
            return self._vad_counter % 10 == 0
        if 'json_data' in record.getMessage() and len(record.getMessage()) > 1000:
            return False
        return True

# Initial basic logging setup
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
root_logger = logging.getLogger()
root_logger.addFilter(ImportantLogsFilter())
logger = logging.getLogger(__name__)

# Globals to be initialized per session
_vector_db_loader: Optional[VectorDBLoader] = None
_metrics_collector = None


class SessionMetricsCollector:
    """
    Collects and manages metrics for a single session, structuring them
    into conversational turns. This version includes corrected logic for
    turn creation and finalization.
    """

    def __init__(self, session_name: str):
        self.session_name = session_name
        self.session_start_time = datetime.now()
        self.metrics = {
            "session_info": {
                "session_name": session_name,
                "start_time": self.session_start_time.isoformat(),
                "end_time": None,
                "duration_seconds": None,
            },
            "structured_conversation": [],
            "usage_summary": None,
        }
        self._current_turn = None
        logger.info(f"📊 SessionMetricsCollector initialized for session: {session_name}")

    def _finalize_turn_if_complete(self):
        """
        If the current turn has both user and assistant content, it's considered complete.
        This method finalizes it by adding it to the main conversation list.
        """
        if self._current_turn:
            user_content = self._current_turn['user_turn'].get('content', '').strip()
            assistant_content = self._current_turn['assistant_turn'].get('content', '').strip()
            # Only finalize if we have both user and assistant content
            if user_content and assistant_content:
                self.metrics["structured_conversation"].append(self._current_turn)
                logger.info(f"✅ Turn finalized: User='{user_content[:50]}...' Assistant='{assistant_content[:50]}...'")
                self._current_turn = None
                return True
        return False

    def _get_or_create_turn(self):
        """
        Gets the current turn object. If it's None (either at the start or after
        a turn was finalized), it creates a new, empty turn structure.
        """
        if self._current_turn is None:
            self._current_turn = {
                "user_turn": {
                    "timestamp": None, "content": "", "metrics": {"stt": [], "vad": [], "eou": []}
                },
                "assistant_turn": {
                    "timestamp": None, "content": "", "function_calls": [], "metrics": {"llm": [], "tts": []}
                },
            }
        return self._current_turn

    def _get_user_turn(self):
        """
        Ensures a new turn is created for each new user utterance. If a previous turn
        was incomplete (e.g., user spoke, but assistant did not respond yet), it is
        considered a new user turn.
        """
        # If there's an existing turn and it has user content, or if it's a new session, create a new turn
        if self._current_turn is not None and self._current_turn['user_turn'].get('content'):
            # If current turn has user content, and assistant hasn't responded yet,
            # or if the turn simply needs to be finalized because it's complete
            # then finalize it (if applicable) and create a new turn.
            self._finalize_turn_if_complete()
            self._current_turn = None # Force creation of a new turn for the next user input
        
        return self._get_or_create_turn()['user_turn']

    def _get_assistant_turn(self):
        """
        Safely gets the assistant_turn. Assistant actions belong to the currently active turn.
        """
        return self._get_or_create_turn()['assistant_turn']

    def add_llm_metrics(self, metrics: LLMMetrics):
        """Add LLM metrics to the current assistant turn."""
        try:
            assistant_turn = self._get_assistant_turn()
            metrics_data = {
                "type": metrics.type, "label": metrics.label, "request_id": metrics.request_id,
                "timestamp": metrics.timestamp if isinstance(metrics.timestamp, (int, float)) else metrics.timestamp.isoformat() if metrics.timestamp else None,
                "duration": metrics.duration, "ttft": metrics.ttft, "cancelled": metrics.cancelled,
                "completion_tokens": metrics.completion_tokens, "prompt_tokens": metrics.prompt_tokens,
                "total_tokens": metrics.total_tokens, "tokens_per_second": metrics.tokens_per_second
            }
            assistant_turn["metrics"]["llm"].append(metrics_data)
        except Exception as e:
            logger.error(f"❌ ERROR adding LLM metrics: {e}", exc_info=True)

    def add_stt_metrics(self, metrics: STTMetrics):
        """Add STT metrics to the current user turn."""
        try:
            user_turn = self._get_user_turn()
            metrics_data = {
                "type": metrics.type, "label": metrics.label, "request_id": metrics.request_id,
                "timestamp": metrics.timestamp if isinstance(metrics.timestamp, (int, float)) else metrics.timestamp.isoformat() if metrics.timestamp else None,
                "duration": metrics.duration, "speech_id": metrics.speech_id,
                "error": str(metrics.error) if metrics.error else None,
                "streamed": metrics.streamed, "audio_duration": metrics.audio_duration
            }
            user_turn["metrics"]["stt"].append(metrics_data)
            if metrics.error: logger.error(f"❌ STT ERROR: {metrics.error}")
        except Exception as e:
            logger.error(f"❌ ERROR adding STT metrics: {e}", exc_info=True)

    def add_tts_metrics(self, metrics: TTSMetrics):
        """Add TTS metrics to the current assistant turn."""
        try:
            assistant_turn = self._get_assistant_turn()
            metrics_data = {
                "type": metrics.type, "label": metrics.label, "request_id": metrics.request_id,
                "timestamp": metrics.timestamp if isinstance(metrics.timestamp, (int, float)) else metrics.timestamp.isoformat() if metrics.timestamp else None,
                "ttfb": metrics.ttfb, "duration": metrics.duration, "audio_duration": metrics.audio_duration,
                "cancelled": metrics.cancelled, "characters_count": metrics.characters_count,
                "streamed": metrics.streamed, "speech_id": metrics.speech_id,
                "error": str(getattr(metrics, 'error', None)) if getattr(metrics, 'error', None) else None
            }
            assistant_turn["metrics"]["tts"].append(metrics_data)
            tts_error = getattr(metrics, 'error', None)
            if tts_error: logger.error(f"❌ TTS ERROR: {tts_error}")
        except Exception as e:
            logger.error(f"❌ ERROR adding TTS metrics: {e}", exc_info=True)

    def add_eou_metrics(self, metrics: EOUMetrics):
        """Add EOU metrics to the current user turn."""
        try:
            user_turn = self._get_user_turn()
            metrics_data = {
                "type": metrics.type, "label": "eou_metrics",
                "timestamp": metrics.timestamp if isinstance(metrics.timestamp, (int, float)) else metrics.timestamp.isoformat() if metrics.timestamp else None,
                "end_of_utterance_delay": metrics.end_of_utterance_delay,
                "transcription_delay": metrics.transcription_delay, "speech_id": metrics.speech_id,
                "error": str(metrics.error) if metrics.error else None
            }
            user_turn["metrics"]["eou"].append(metrics_data)
            if metrics.error: logger.error(f"❌ EOU ERROR: {metrics.error}")
        except Exception as e:
            logger.error(f"❌ ERROR adding EOU metrics: {e}", exc_info=True)

    def add_vad_metrics(self, event):
        """Add VAD metrics to the current user turn."""
        try:
            user_turn = self._get_user_turn()
            metrics_data = {
                "type": getattr(event, 'type', 'vad_metrics'),
                "timestamp": event.timestamp if isinstance(event.timestamp, (int, float)) else event.timestamp.isoformat() if hasattr(event, 'timestamp') and event.timestamp else None,
                "idle_time": getattr(event, 'idle_time', None),
                "inference_duration_total": getattr(event, 'inference_duration_total', None),
                "inference_count": getattr(event, 'inference_count', None),
                "speech_id": getattr(event, 'speech_id', None),
                "error": str(getattr(event, 'error', None)) if getattr(event, 'error', None) else None
            }
            user_turn["metrics"]["vad"].append(metrics_data)
            vad_error = getattr(event, 'error', None)
            if vad_error: logger.error(f"❌ VAD ERROR: {vad_error}")
        except Exception as e:
            logger.error(f"❌ ERROR adding VAD metrics: {e}", exc_info=True)

    def add_conversation_entry(self, role: str, content: str):
        """Add a conversation entry to the appropriate turn."""
        if not content: return

        timestamp = datetime.now().isoformat()
        if role == "user":
            user_turn = self._get_user_turn()
            if not user_turn["timestamp"]: user_turn["timestamp"] = timestamp
            user_turn["content"] = content.strip()
            logger.info(f"📝 User: {content[:80].strip()}...")
        elif role == "assistant":
            assistant_turn = self._get_assistant_turn()
            if not assistant_turn["timestamp"]: assistant_turn["timestamp"] = timestamp
            assistant_turn["content"] = content.strip()
            logger.info(f"🤖 Assistant: {content[:80].strip()}...")
            # Try to finalize the turn now that assistant has responded
            self._finalize_turn_if_complete()

    def add_function_call(self, function_name: str, args: dict, result: str = None):
        """Add a function call to the current assistant turn."""
        assistant_turn = self._get_assistant_turn()
        entry = {
            "timestamp": datetime.now().isoformat(),
            "function_name": function_name, "arguments": args, "result": result,
        }
        assistant_turn["function_calls"].append(entry)
        logger.info(f"Function call: {function_name}")

    def set_usage_summary(self, summary: str):
        """Set the usage summary for the session."""
        self.metrics["usage_summary"] = summary

    def finalize_session(self):
        """Finalize metrics with end time, duration, and the last pending turn."""
        end_time = datetime.now()
        self.metrics["session_info"]["end_time"] = end_time.isoformat()
        self.metrics["session_info"]["duration_seconds"] = (end_time - self.session_start_time).total_seconds()

        # Try to finalize any complete turn first
        self._finalize_turn_if_complete()
        
        # Add incomplete turn if it has any meaningful content
        if self._current_turn:
            user_content = self._current_turn.get("user_turn", {}).get("content", "").strip()
            assistant_content = self._current_turn.get("assistant_turn", {}).get("content", "").strip()
            if user_content or assistant_content:
                logger.info(f"📝 Adding incomplete turn: User='{user_content[:50] if user_content else 'None'}' Assistant='{assistant_content[:50] if assistant_content else 'None'}'")
                self.metrics["structured_conversation"].append(self._current_turn)
            self._current_turn = None

    def save_to_json(self):
        """Save structured metrics to a JSON file."""
        try:
            self.finalize_session()
            filename = f"{self.session_name}_metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            filepath = os.path.join(METRICS_DIR, filename)

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.metrics, f, indent=2, ensure_ascii=False)

            logger.info(f"✅ Session metrics saved to: {filepath}")
            turn_count = len(self.metrics.get('structured_conversation', []))
            logger.info(f"📊 Session summary - Turns captured: {turn_count}")

        except Exception as e:
            logger.error(f"❌ Failed to save metrics to JSON: {e}", exc_info=True)


async def get_vector_db_loader() -> VectorDBLoader:
    """Lazily initialize VectorDBLoader in a background thread and return the instance."""
    global _vector_db_loader
    if _vector_db_loader is None:
        loop = asyncio.get_running_loop()
        _vector_db_loader = await loop.run_in_executor(None, VectorDBLoader)
    return _vector_db_loader

@function_tool
async def search_knowledge_base(
    query: Annotated[
        str,
        """Convert current user's query into a comprehensive search query,
        optimized for vector similarity matching.
        """
    ]
) -> str:
    """
    Searches the internal knowledge base for accurate answers about P2P lending,
    Lenden club,investment products, services, founders, and other related topics.

    This is the **primary and only** source for responding to user queries on these subjects.
    Use this tool **only for directly relevant questions**, and refer to **conversation history** to
    avoid redundant lookups of previously fetched information.

    Args:
        query (str): A user question converted into a search-optimized query string.

    Returns:
        str: Formatted Q&A pairs retrieved from the knowledge base.
    """
    global _metrics_collector
    loader = await get_vector_db_loader()
    results = await loader.search(query, top_k=2)

    if not results:
        result = "I'm sorry, I couldn't find relevant information in the knowledge base."
    else:
        qa_texts = [f"{idx}. {item.get('formatted_text', '').strip()}" for idx, item in enumerate(results, start=1)]
        result = "\n\n".join(qa_texts)

    if _metrics_collector:
        _metrics_collector.add_function_call("search_knowledge_base", {"query": query}, result[:200] + "..." if len(result) > 200 else result)

    return result

@function_tool
async def web_search(
    query: Annotated[
        str,
        "Make query as specific as possible with additional context. "
        "Transform 'what's the weather' to 'what is the weather in [city] "
        "today'. Add relevant constraints, dates, or location details for "
        "better results."
    ]
) -> str:
    """
    Search web for real-time information.

    Search for current events, news, weather, stock prices, or recent data
    not in knowledge base. Called when user asks to search web, google
    something, or requests current/live information.

    Args:
        query: The web search query string.

    Returns:
        str: The search result or error message.
    """
    global _metrics_collector
    try:
        api_key = os.getenv('LINKUP_API_KEY')
        client = LinkupClient(api_key=api_key)

        def do_search():
            return client.search(
                query=query,
                depth="standard",
                output_type="sourcedAnswer",
            )

        loop = asyncio.get_running_loop()
        response = await loop.run_in_executor(None, do_search)

        if hasattr(response, 'answer') and response.answer:
            result = response.answer
        else:
            result = "No direct answer found. Try rephrasing your question."

    except Exception as e:
        logger.error(f"Web search error: {e}")
        result = "Web search error. Please try again later."

    if _metrics_collector:
        _metrics_collector.add_function_call("web_search", {"query": query}, result[:200] + "..." if len(result) > 200 else result)

    return result

# Global variable to track report requests across sessions
_report_requested_global = False

@function_tool
async def get_report_with_verification(
    last_four_digits: Annotated[
        str,
        "The last 4 digits of the user's registered mobile number for verification."
    ]
) -> str:
    """
    ONLY call this function when the user explicitly asks for their report, statement, or account statement.
    This generates and emails the user's FMPP account statement for the last 12 months.
    Requires verification of last 4 digits of registered mobile number, ask the user for this before calling the function.
    """
    global _report_requested_global, _metrics_collector

    if _metrics_collector:
        _metrics_collector.add_function_call("get_report_with_verification", {"last_four_digits": last_four_digits}, "Attempting verification...")

    registered_numbers = ["**********", "**********", "**********", "**********"]

    if _report_requested_global:
        logger.warning("🚫 Report already requested in this session")
        return ("Your statement for this session has already been generated. "
                "You can only request one statement per session")

    def convert_hindi_to_english(text):
        hindi_to_english = {'०': '0', '१': '1', '२': '2', '३': '3', '४': '4', '५': '5', '६': '6', '७': '7', '८': '8', '९': '9'}
        result = text
        for hindi, english in hindi_to_english.items():
            result = result.replace(hindi, english)
        return result

    def extract_digits(text):
        text = convert_hindi_to_english(text)
        return ''.join(filter(str.isdigit, text))

    try:
        logger.info("📋 Verifying last 4 digits for report generation")
        input_digits = extract_digits(last_four_digits)

        if len(input_digits) < 4:
            logger.warning(f"🚫 Insufficient digits provided: {input_digits}")
            return ("Please provide the complete four digits of your registered mobile number. "
                    "For example, if your number ends in 4566, you would provide '4566'.")

        user_last_four = input_digits[-4:]
        logger.info(f"🔍 User provided last 4 digits: {user_last_four}")

        matched_number = next((number for number in registered_numbers if number.endswith(user_last_four)), None)

        if not matched_number:
            logger.warning(f"🚫 No matching number found for digits: {user_last_four}")
            return (f"The last 4 digits you provided ({user_last_four}) do not match our records. "
                    "Please confirm you are using the correct registered mobile number.")

        logger.info(f"✅ Phone number verified: {matched_number}")
        _report_requested_global = True
        contact_number = matched_number

        url = f"https://qa-investor-api.lendenclub.com/api/ims/common/v1/get-user-token?mobile_number={contact_number}"
        response = requests.get(url)

        if response.status_code != 200:
            logger.error(f"❌ Failed to get user token. Status code: {response.status_code}")
            _report_requested_global = False
            return (f"There was an issue generating the report for contact number {contact_number}. "
                    "Please check your mobile number or contact customer support.")

        token_info = response.json().get('data', {})
        access_token = token_info.get('access_token')
        ldc_code = token_info.get('ldc_code')

        if not access_token or not ldc_code:
            logger.error("❌ Invalid token response")
            _report_requested_global = False
            return "There was an issue verifying your account details. Please contact customer support."

        headers = {
            "Authorization": f"Token {access_token}",
            "x-ldc-key": ldc_code,
            "Referer": "https://qa-app.lendenclub.com/",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
            "Content-Type": "application/json"
        }
        params = {"statement_type": "FMPP_ACCOUNT_STATEMENT", "months": "12", "send_email": "true"}
        report_url = "https://qa-investor-api.lendenclub.com/api/ims/retail-investor/v5/web/statement-type"
        email_response = requests.get(report_url, headers=headers, params=params)

        if email_response.status_code == 200:
            logger.info("✅ Report generated successfully")
            return (f"✅ आपकी 12-month FMPP account statement successfully generate हो गई है और आपके "
                    f"registered email address पर send कर दी गई है। कृपया अपना email check करें "
                    f"(spam folder भी देखें)। Contact number: {contact_number}")
        else:
            logger.error(f"❌ Report generation failed. Status code: {email_response.status_code}")
            _report_requested_global = False
            return (f"Report generate करने में technical issue आया है। Response code: {email_response.status_code}। "
                    "कृपया कुछ देर बाद try करें या customer support से संपर्क करें।")

    except requests.RequestException as e:
        logger.error(f"❌ Network error in get_report: {str(e)}")
        _report_requested_global = False
        return "Network connection में समस्या के कारण report generate नहीं हो सकी। कृपया अपना internet connection check करें और फिर से try करें।"
    except Exception as e:
        logger.error(f"❌ Unexpected error in get_report: {str(e)}")
        _report_requested_global = False
        return "Report generate करने में technical error आया है। कृपया customer support से संपर्क करें।"


class NeuraVoiceAgent(Agent):
    """
    Main voice agent for P2P lending conversations.
    Handles conversations, retrieves information, and answers queries.
    """
    def __init__(self, chat_ctx: ChatContext, metrics_collector: SessionMetricsCollector) -> None:
        super().__init__(
            instructions=get_enhanced_system_prompt(),
            chat_ctx=chat_ctx,
            tools=[search_knowledge_base, web_search, get_report_with_verification]
        )
        self.metrics_collector = metrics_collector

    async def on_enter(self) -> None:
        """Handle agent initialization and greeting."""
        greeting_instruction = ("Greet the user warmly and ask them how you can help them "
                                "today about p2p lending.")
        try:
            await self.session.generate_reply(instructions=greeting_instruction)
            logger.info("📝 [GREETING] Agent greeting generated successfully")
        except Exception as e:
            logger.error(f"Error generating greeting: {e}")

def prewarm(proc: JobProcess):
    """Pre-warms resources before the agent starts."""
    proc.userdata["vad"] = silero.VAD.load()

async def entrypoint(ctx: JobContext):
    """The main entrypoint for the voice agent worker."""
    logger.info("🚀 Starting agent entrypoint...")
    try:
        ctx.log_context_fields = {"room": ctx.room.name}
        session_name = ctx.room.name or f"session_{int(time.time())}"
        metrics_collector = SessionMetricsCollector(session_name)

        log_file_path = os.path.join(LOGS_DIR, f"{session_name}.log")
        file_handler = logging.FileHandler(log_file_path)
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(name)s - %(message)s'))
        file_handler.addFilter(ImportantLogsFilter())
        root_logger.addHandler(file_handler)

        logger.info(f"📊 Created metrics collector for session: {session_name}")
        logger.info(f"📝 Session logs will be saved to: {log_file_path}")

        global _metrics_collector
        _metrics_collector = metrics_collector

        stt = openai.STT(model='gpt-4o-mini-transcribe')
        llm = openai.LLM(model='gpt-4o-mini', temperature=0.6)
        tts = google.TTS(
            language='hi-IN', gender='female', voice_name='hi-IN-Chirp3-HD-Laomedeia',
            sample_rate=24000, speaking_rate=0.95, use_streaming=True,
            location='asia-southeast1', credentials_file=os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
        )
        vad_component = ctx.proc.userdata["vad"]

        def create_metric_handler(metric_adder):
            def wrapper(metrics):
                try:
                    asyncio.create_task(asyncio.to_thread(metric_adder, metrics))
                except Exception as e:
                    logger.error(f"❌ ERROR in metrics wrapper for {metric_adder.__name__}: {e}", exc_info=True)
            return wrapper

        llm.on("metrics_collected", create_metric_handler(metrics_collector.add_llm_metrics))
        stt.on("metrics_collected", create_metric_handler(metrics_collector.add_stt_metrics))
        stt.on("eou_metrics_collected", create_metric_handler(metrics_collector.add_eou_metrics))
        tts.on("metrics_collected", create_metric_handler(metrics_collector.add_tts_metrics))
        vad_component.on("metrics_collected", create_metric_handler(metrics_collector.add_vad_metrics))

        USER_AWAY_TIMEOUT_S = 30.0
        session = AgentSession(
            stt=stt, vad=vad_component, turn_detection=MultilingualModel(), llm=llm, tts=tts,
            allow_interruptions=True, user_away_timeout=USER_AWAY_TIMEOUT_S
        )

        inactivity_task: asyncio.Task | None = None

        async def user_presence_task():
            logger.warning(f"User has been inactive for {USER_AWAY_TIMEOUT_S}s. Starting presence check...")
            try:
                for i in range(3):
                    logger.info(f"Performing presence check #{i+1}/3...")
                    await session.generate_reply(
                        instructions="The user has been inactive. Politely check if the user is still present, based on the conversation history. Keep it very short."
                    )
                    await asyncio.sleep(15)
                logger.warning("User did not respond. Ending session.")
                await asyncio.shield(session.aclose())
            except asyncio.CancelledError:
                logger.info("User presence check cancelled; user returned.")
            except Exception as e:
                logger.error(f"Error in user presence check: {e}", exc_info=True)
                await asyncio.shield(session.aclose())

        @session.on("user_state_changed")
        def _on_user_state_changed(ev: UserStateChangedEvent):
            nonlocal inactivity_task
            if ev.new_state == "away":
                if inactivity_task is None or inactivity_task.done():
                    inactivity_task = asyncio.create_task(user_presence_task())
            elif inactivity_task is not None:
                inactivity_task.cancel()
                inactivity_task = None

        usage_collector = metrics.UsageCollector()
        @session.on("metrics_collected")
        def _on_metrics_collected(event: MetricsCollectedEvent):
            metrics.log_metrics(event.metrics)
            usage_collector.collect(event.metrics)

        @session.on("conversation_item_added")
        def _on_conversation_item_added(event: ConversationItemAddedEvent):
            try:
                item = getattr(event, "item", None)
                if not item: 
                    logger.debug("📝 No item in conversation event")
                    return

                role = getattr(item, "role", "")
                content_parts = getattr(item, "content", [])
                
                logger.debug(f"📝 Raw conversation item: role='{role}', content_type={type(content_parts)}")
                
                content = (content_parts if isinstance(content_parts, str) else 
                           "".join(str(part.get("text", "") if isinstance(part, dict) else getattr(part, "text", "")) 
                                   for part in content_parts)).strip()

                if content and role in ["user", "assistant"]:
                    logger.debug(f"📝 Processing conversation: {role} - {content[:100]}...")
                    metrics_collector.add_conversation_entry(role, content)
                else:
                    logger.debug(f"📝 Skipping conversation item: role='{role}', content_length={len(content) if content else 0}")

            except Exception as e:
                logger.error(f"Conversation logging error: {e}", exc_info=True)

        async def log_usage_and_save_metrics():
            """Shutdown callback to log usage and save all metrics."""
            summary = usage_collector.get_summary()
            logger.info(f"Usage: {summary}")
            metrics_collector.set_usage_summary(str(summary))
            await asyncio.to_thread(metrics_collector.save_to_json)

        logger.info("✅ Event handlers configured.")
        ctx.add_shutdown_callback(log_usage_and_save_metrics)

        bot = NeuraVoiceAgent(chat_ctx=ChatContext(), metrics_collector=metrics_collector)
        logger.info("✅ NeuraVoiceAgent created")
        asyncio.create_task(get_vector_db_loader())

        await session.start(
            agent=bot,
            room=ctx.room,
            room_input_options=RoomInputOptions(noise_cancellation=noise_cancellation.BVC()),
            room_output_options=RoomOutputOptions(transcription_enabled=True),
        )

        background_audio = BackgroundAudioPlayer(
            ambient_sound=AudioConfig(BuiltinAudioClip.OFFICE_AMBIENCE, volume=0.8)
        )
        await background_audio.start(room=ctx.room, agent_session=session)
        logger.info("✅ Agent session started with background audio.")

    except asyncio.CancelledError:
        logger.info("Agent session was cancelled.")
    except Exception as e:
        logger.critical(f"❌ CRITICAL ERROR in entrypoint: {e}", exc_info=True)
        if 'metrics_collector' in locals() and isinstance(metrics_collector, SessionMetricsCollector):
            await asyncio.to_thread(metrics_collector.save_to_json)
        raise

if __name__ == "__main__":
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            job_memory_warn_mb=max(MEMORY_WARN_MB, 1024),
            prewarm_fnc=prewarm,
            job_memory_limit_mb=0,
            initialize_process_timeout=25
        )
    )
