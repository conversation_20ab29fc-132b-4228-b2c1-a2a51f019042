"""Conversation logging module for LiveKit voice agents.

This file defines `ConversationLogger` – a JSONL logger that tracks every
conversation turn and session in *structured* form, ready for analytics or
retraining pipelines.

Major features
--------------
1. Turn-level and session-level dataclasses serialisable to JSON.
2. Intent classification, entity extraction and language detection.
3. Enhanced **function call** tracking (multiple per turn or session).
4. NEW: **feedback** capture – if a user explicitly provides feedback it is
   stored in the `feedback` field of that turn.
5. A helper `integrate_with_livekit_agent()` factory that returns a thin
   wrapper class you can plug into LiveKit callbacks immediately.

This file is self-contained (only standard-library dependencies) and can be
imported from `nv_demo_final.py` or any other module:

    from conversation_logger import ConversationLogger
    conv_logger = ConversationLogger("logs/conversations.jsonl")
    session_id = conv_logger.start_session()
    conv_logger.log_turn("user", "Hi there!", feedback="👍 Great bot")
    conv_logger.end_session()
"""

from __future__ import annotations

import json
import re
import time
import uuid
import logging
from dataclasses import dataclass, asdict
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)

# ---------------------------------------------------------------------------
#                           Data Structures
# ---------------------------------------------------------------------------

@dataclass
class TurnData:
    """Structured information captured for *each* conversation turn."""

    turn_id: int
    timestamp: str
    speaker: str  # "user" or "bot"
    transcript: str

    # Optional analytics
    intent: Optional[str] = None
    entities: Dict[str, Any] | None = None
    confidence_score: Optional[float] = None
    input_tokens: Optional[int] = None
    output_tokens: Optional[int] = None

    # Function-call tracking
    functions_called: List[str] | None = None  # backwards-compatible below
    function_called: Optional[bool] = None

    # NEW — explicit user feedback string, if provided for this turn
    feedback: Optional[str] = None

    # ------------------------------------------------------------------
    def __post_init__(self) -> None:
        if self.entities is None:
            self.entities = {}
        if self.functions_called is None:
            self.functions_called = []
        # backwards compatibility flag
        self.function_called = len(self.functions_called) > 0
        if self.feedback is None:
            self.feedback = ""


@dataclass
class SessionData:
    """Information about the whole conversation session."""

    session_id: str
    start_time: str

    end_time: Optional[str] = None
    call_duration_seconds: Optional[int] = None

    language_detected: Optional[str] = None
    turns: List[TurnData] | None = None

    session_outcome: str = "in_progress"
    summary: Optional[str] = None
    issue_tags: List[str] | None = None

    retrain_flag: bool = False
    total_tokens: int = 0

    # Out-of-context questions
    out_of_context_questions: int = 0
    out_of_context_questions_answered: int = 0

    # Function-call tracking (session aggregate)
    functions_called: List[str] | None = None
    function_called: bool = False

    # NEW — overall session feedback if you decide to collect one-shot rating
    session_feedback: Optional[str] = None

    # ------------------------------------------------------------------
    def __post_init__(self) -> None:
        if self.turns is None:
            self.turns = []
        if self.issue_tags is None:
            self.issue_tags = []
        if self.functions_called is None:
            self.functions_called = []
        self.function_called = len(self.functions_called) > 0
        if self.session_feedback is None:
            self.session_feedback = ""


# ---------------------------------------------------------------------------
#                       ConversationLogger class
# ---------------------------------------------------------------------------

class ConversationLogger:
    """Comprehensive conversation logger with JSONL persistence."""

    # --- Intent patterns (basic rule-based demo) ----------------------
    _INTENT_PATTERNS: Dict[str, List[str]] = {
        "onboarding_inquiry": [
            r"invest.*lenden|signup.*process|how.*join|registration",
            r"account.*create|getting.*started|onboard",
        ],
        "product_inquiry": [
            r"what.*is.*p2p|lending.*works|returns.*rate|interest.*rate",
            r"investment.*option|portfolio.*analysis",
        ],
        "technical_support": [
            r"login.*problem|app.*not.*working|technical.*issue|bug",
            r"password.*reset|account.*locked",
        ],
        "general_inquiry": [
            r"hello|hi|good.*morning|good.*evening|greet",
            r"thank.*you|thanks|goodbye|bye",
        ],
        "complaint": [
            r"problem.*with|issue.*with|not.*satisfied|complaint",
            r"refund|cancel.*investment",
        ],
    }

    # ------------------------------------------------------------------
    def __init__(self, log_file_path: str = "conversation_logs.jsonl", *, enable_summary_generation: bool = True):
        self.log_file_path = Path(log_file_path)
        self.enable_summary_generation = enable_summary_generation

        self.current_session: Optional[SessionData] = None
        self.turn_counter: int = 0
        self.session_start_time: Optional[float] = None

        # Ensure folder exists
        self.log_file_path.parent.mkdir(parents=True, exist_ok=True)
        logger.info("ConversationLogger initialised → %s", self.log_file_path)

    # ------------------------------------------------------------------
    #                         Session helpers
    # ------------------------------------------------------------------
    def start_session(self, session_id: str | None = None) -> str:
        if session_id is None:
            session_id = str(uuid.uuid4())
        self.current_session = SessionData(
            session_id=session_id,
            start_time=datetime.now(timezone.utc).isoformat(),
        )
        self.turn_counter = 0
        self.session_start_time = time.time()
        logger.info("🆕 [SESSION START] %s", session_id)
        return session_id

    def end_session(self, *, outcome: str = "completed", issue_tags: List[str] | None = None, retrain_flag: bool = False, session_feedback: str | None = None) -> Dict[str, Any]:
        if not self.current_session:
            logger.warning("No active session to end")
            return {}

        # If no turns, skip writing to log file
        if not self.current_session.turns or len(self.current_session.turns) == 0:
            logger.info(f"[SESSION END] Session {self.current_session.session_id} has no turns, skipping log write.")
            # Reset state
            self.current_session = None
            self.turn_counter = 0
            self.session_start_time = None
            return {}

        # Populate meta
        if self.session_start_time:
            self.current_session.call_duration_seconds = int(time.time() - self.session_start_time)
        self.current_session.end_time = datetime.now(timezone.utc).isoformat()
        self.current_session.session_outcome = outcome
        self.current_session.retrain_flag = retrain_flag
        if issue_tags:
            self.current_session.issue_tags = issue_tags
        if session_feedback:
            self.current_session.session_feedback = session_feedback

        # Compute answered OOC count (very naive heuristic)
        self.current_session.out_of_context_questions_answered = max(
            0,
            self.current_session.out_of_context_questions
            - len([t for t in self.current_session.turns if t.speaker == "bot" and "don't know" in t.transcript.lower()]),
        )

        if self.enable_summary_generation:
            self.current_session.summary = self._generate_summary()

        session_dict = asdict(self.current_session)
        self._save_session_log(session_dict)
        logger.info("🏁 [SESSION END] %s", self.current_session.session_id)

        # Reset state
        self.current_session = None
        self.turn_counter = 0
        self.session_start_time = None
        return session_dict

    # ------------------------------------------------------------------
    #                          Turn logging
    # ------------------------------------------------------------------
    def log_turn(
        self,
        speaker: str,
        transcript: str,
        *,
        input_tokens: int | None = None,
        output_tokens: int | None = None,
        function_called: bool = False,
        functions_called: List[str] | None = None,
        feedback: str | None = None,
    ) -> TurnData:
        """Append a single turn to the session log."""
        if not self.current_session:
            logger.warning("No active session – auto-starting one.")
            self.start_session()

        # --- language detection (once per session) ------------------
        if not self.current_session.language_detected:
            self.current_session.language_detected = self._detect_language(transcript)

        # --- analytics (only for user) ------------------------------
        intent: str | None = None
        entities: Dict[str, Any] = {}
        confidence: float | None = None
        if speaker == "user":
            intent, confidence = self._classify_intent(transcript)
            entities = self._extract_entities(transcript)
            if self._is_out_of_context(transcript):
                self.current_session.out_of_context_questions += 1

        # --- token estimates ----------------------------------------
        if input_tokens is None and speaker == "user":
            input_tokens = self._estimate_tokens(transcript)
        if output_tokens is None and speaker == "bot":
            output_tokens = self._estimate_tokens(transcript)
        if input_tokens:
            self.current_session.total_tokens += input_tokens
        if output_tokens:
            self.current_session.total_tokens += output_tokens

        # --- function-call tracking ---------------------------------
        fn_list = functions_called or []
        if function_called and not fn_list:
            fn_list = ["unknown_function"]
        for fn in fn_list:
            if fn not in self.current_session.functions_called:
                self.current_session.functions_called.append(fn)
        if fn_list:
            self.current_session.function_called = True

        # --- Build TurnData -----------------------------------------
        self.turn_counter += 1
        turn = TurnData(
            turn_id=self.turn_counter,
            timestamp=datetime.now(timezone.utc).isoformat(),
            speaker=speaker,
            transcript=transcript,
            intent=intent,
            entities=entities,
            confidence_score=confidence,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            functions_called=fn_list.copy(),
            function_called=len(fn_list) > 0,
            feedback=feedback if feedback else "",
        )
        self.current_session.turns.append(turn)
        logger.info("📝 [TURN] #%d | %s%s", self.turn_counter, speaker, f" | feedback" if feedback else "")
        return turn

    # ------------------------------------------------------------------
    #                        Small helper methods
    # ------------------------------------------------------------------
    def _detect_language(self, text: str) -> str:
        hindi_chars = len(re.findall(r"[\u0900-\u097F]", text))
        english_words = len(re.findall(r"[a-zA-Z]+", text))
        total_chars = len(text.strip())
        if total_chars == 0:
            return "unknown"
        hindi_ratio = hindi_chars / total_chars
        english_ratio = english_words / total_chars
        if hindi_ratio > 0.3:
            return "Hinglish" if english_ratio > 0.2 else "Hindi"
        if english_ratio > 0.5:
            return "English"
        return "Mixed"

    def _classify_intent(self, text: str) -> tuple[str, float]:
        tl = text.lower()
        best_intent, best_score = "unknown", 0.0
        for intent, patterns in self._INTENT_PATTERNS.items():
            matches = sum(1 for p in patterns if re.search(p, tl))
            if matches:
                score = matches / len(patterns)
                if score > best_score:
                    best_intent, best_score = intent, score
        return best_intent, min(best_score * 2, 1.0)

    def _extract_entities(self, text: str) -> Dict[str, Any]:
        ents: Dict[str, Any] = {}
        nums = re.findall(r"\b\d+(?:\.\d+)?\b", text)
        if nums:
            ents["numbers"] = nums
        emails = re.findall(r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}\b", text)
        if emails:
            ents["emails"] = emails
        phones = re.findall(r"\b(?:\+91|91)?[6-9]\d{9}\b", text)
        if phones:
            ents["phone_numbers"] = phones
        amounts = re.findall(r"₹\s*\d+(?:,\d+)*(?:\.\d+)?|rs\.?\s*\d+", text, re.IGNORECASE)
        if amounts:
            ents["amounts"] = amounts
        return ents

    def _is_out_of_context(self, text: str) -> bool:
        keywords = [
            "invest",
            "lending",
            "loan",
            "returns",
            "interest",
            "portfolio",
            "lenden",
            "club",
            "p2p",
            "borrower",
            "lender",
            "rbi",
            "nbfc",
            "risk",
            "credit",
            "finance",
            "money",
            "investment",
        ]
        tl = text.lower()
        if any(k in tl for k in keywords):
            return False
        generic = [r"\b(hello|hi|hey|thank you|thanks|bye|goodbye)\b", r"\b(yes|no|ok|okay|sure)\b"]
        if any(re.search(p, tl) for p in generic):
            return False
        return True

    @staticmethod
    def _estimate_tokens(text: str) -> int:
        return max(1, len(text) // 4)

    # ------------------------------------------------------------------
    def _generate_summary(self) -> str:
        if not self.current_session or not self.current_session.turns:
            return "No conversation to summarise."
        user_turns = [t for t in self.current_session.turns if t.speaker == "user"]
        intents = [t.intent for t in user_turns if t.intent and t.intent != "unknown"]
        main_intent = max(set(intents), key=intents.count) if intents else "general"
        parts: List[str] = []
        if user_turns:
            first = user_turns[0].transcript
            parts.append(f"User started with: '{first[:50]}{'...' if len(first) > 50 else ''}'")
        if main_intent != "general":
            parts.append(f"Topic: {main_intent.replace('_', ' ')}")
        if self.current_session.functions_called:
            parts.append(f"Functions called: {', '.join(self.current_session.functions_called)}")
        if self.current_session.out_of_context_questions:
            parts.append(f"OOC questions: {self.current_session.out_of_context_questions}")
        if self.current_session.session_outcome == "completed":
            parts.append("Session completed")
        return ". ".join(parts) + "."

    # ------------------------------------------------------------------
    def _save_session_log(self, data: Dict[str, Any]) -> None:
        try:
            with open(self.log_file_path, "a", encoding="utf-8") as fh:
                json.dump(data, fh, ensure_ascii=False)
                fh.write("\n")
            logger.info("💾 Saved session → %s", self.log_file_path)
        except Exception as exc:
            logger.exception("Failed to save session: %s", exc)

    # ------------------------------------------------------------------
    def get_session_summary(self) -> Optional[Dict[str, Any]]:
        if not self.current_session:
            return None
        return {
            "session_id": self.current_session.session_id,
            "duration_seconds": int(time.time() - self.session_start_time) if self.session_start_time else 0,
            "total_turns": len(self.current_session.turns),
            "total_tokens": self.current_session.total_tokens,
            "language": self.current_session.language_detected,
            "out_of_context_questions": self.current_session.out_of_context_questions,
            "functions_called": self.current_session.functions_called,
            "unique_functions_count": len(self.current_session.functions_called),
        }


# ---------------------------------------------------------------------------
#                  Optional wrapper for LiveKit integration
# ---------------------------------------------------------------------------

def integrate_with_livekit_agent(log_file_path: str = "livekit_conversation_logs.jsonl"):
    """Return a drop-in helper object compatible with LiveKit agent callbacks."""

    conv_logger = ConversationLogger(log_file_path=log_file_path)

    class LiveKitConversationLogger:
        def on_session_start(self, session_id: str | None = None):
            return conv_logger.start_session(session_id)

        def on_user_speech_committed(self, text: str, *, input_tokens: int | None = None, feedback: str | None = None, functions_called: list | None = None):
            conv_logger.log_turn("user", text, input_tokens=input_tokens, feedback=feedback, functions_called=functions_called)

        def on_agent_speech_committed(self, text: str, *, output_tokens: int | None = None, functions_called: list | None = None):
            conv_logger.log_turn("bot", text, output_tokens=output_tokens, functions_called=functions_called)

        def on_session_end(self, outcome: str = "completed", *, session_feedback: str | None = None):
            return conv_logger.end_session(outcome=outcome, session_feedback=session_feedback)

        def get_current_session_stats(self):
            return conv_logger.get_session_summary()

    return LiveKitConversationLogger()
