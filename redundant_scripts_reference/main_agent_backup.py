"""
Neura Voice P2P Lending Agent

This module provides a voice-enabled agent for P2P lending queries using
LlamaIndex for RAG (Retrieval-Augmented Generation) and vector search.
"""

# Standard library imports
import asyncio
import logging
import os
import sys
import time
from typing import Annotated, Optional

# Third-party imports
import requests
from dotenv import load_dotenv
from linkup import LinkupClient

# Local imports
from prompt import get_enhanced_system_prompt
from clients.retrive import VectorDBLoader

# LiveKit imports
from livekit.plugins.turn_detector.multilingual import MultilingualModel
from livekit.agents import Agent, AgentSession, JobContext, WorkerOptions, cli, UserStateChangedEvent, metrics, MetricsCollectedEvent, JobProcess, AudioConfig, BackgroundAudioPlayer, BuiltinAudioClip
from livekit.plugins import openai, noise_cancellation, google, silero
from livekit.agents.llm import ChatContext, function_tool
from livekit.agents.voice.room_io import RoomInputOptions, RoomOutputOptions



# Load environment variables
load_dotenv()

# Constants
LOGS_DIR = "logs"
PERSIST_DIR = "./neura-voice-p2p-engine-storage"
DOCS_DIR = "docs"
QUESTION_COL = "question"
ANSWER_COL = "answer"
BATCH_SIZE = 100
MEMORY_WARN_MB = 1024

# Create logs directory
os.makedirs(LOGS_DIR, exist_ok=True)

# Application-level logging setup (for non-session specific logs)
app_log_file_path = os.path.join(
    LOGS_DIR, f"nv_app_{time.strftime('%Y%m%d-%H%M%S')}.log"
)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(app_log_file_path),
        logging.StreamHandler(sys.stdout)
    ]
)
app_logger = logging.getLogger("neura_voice_app")

_vector_db_loader: Optional[VectorDBLoader] = None
_report_requested: bool = False


 
class SessionLogger:  
    """Session-specific logger that creates separate log files for each session."""  
      
    def __init__(self, session_id: str, log_dir: str = LOGS_DIR):  
        self.session_id = session_id  
        self.log_file = os.path.join(log_dir, f"session_{session_id}_{time.strftime('%Y%m%d-%H%M%S')}.log")  
          
        # Create session-specific logger  
        self.logger = logging.getLogger(f"session_{session_id}")  
        self.logger.setLevel(logging.INFO)  
          
        # Prevent duplicate handlers if logger already exists  
        if not self.logger.handlers:  
            # File handler for session logs  
            file_handler = logging.FileHandler(self.log_file)  
            file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))  
            self.logger.addHandler(file_handler)  
              
            # Console handler for session logs (optional)  
            console_handler = logging.StreamHandler(sys.stdout)  
            console_handler.setFormatter(logging.Formatter(f'[{session_id}] %(asctime)s - %(levelname)s - %(message)s'))  
            self.logger.addHandler(console_handler)  
      
    def info(self, message: str):  
        self.logger.info(message)  
      
    def error(self, message: str):  
        self.logger.error(message)  
      
    def warning(self, message: str):  
        self.logger.warning(message)  
      
    def debug(self, message: str):  
        self.logger.debug(message)  
  

async def get_vector_db_loader() -> VectorDBLoader:
    """Lazily initialize VectorDBLoader in a background thread and return the instance."""
    global _vector_db_loader
    if _vector_db_loader is None:
        app_logger.info("Initializing VectorDBLoader...")
        loop = asyncio.get_running_loop()
        _vector_db_loader = await loop.run_in_executor(None, VectorDBLoader)
        app_logger.info("✅ VectorDBLoader initialized successfully")
    return _vector_db_loader

@function_tool
async def search_knowledge_base(
    query: Annotated[
        str,
        """Convert user's query into a comprehensive search query,
        optimized for vector similarity matching.
        """
    ]
) -> str:
    """
   
    Searches the internal knowledge base for accurate answers about P2P lending, 
    Lenden club,investment products, services, founders, and other related topics.
     
    This is the **primary and only** source for responding to user queries on these subjects.  
    Use this tool **only for directly relevant questions**, and refer to **conversation history** to 
    avoid redundant lookups of previously fetched information.

    Args:
        query (str): A user question converted into a search-optimized query string.

    Returns:
        str: Formatted Q&A pairs retrieved from the knowledge base.


    """
    try:
        app_logger.info(f"🔍 Searching knowledge base for: {query}")
        loader = await get_vector_db_loader()
        results = await loader.search(query, top_k=2)
        
        if not results:
            app_logger.warning("No results found in knowledge base search")
            return "I'm sorry, I couldn't find relevant information in the knowledge base."
        
        app_logger.info(f"Found {len(results)} results in knowledge base")
        qa_texts = [f"{idx}. {item.get('formatted_text', '').strip()}" for idx, item in enumerate(results, start=1)]
        return "\n\n".join(qa_texts)
    except Exception as e:
        app_logger.error(f"Error in knowledge base search: {e}")
        return "I'm sorry, there was an error searching the knowledge base. Please try again."


@function_tool
async def get_report() -> str:
    """
    ONLY call this function when the user explicitly asks for their report, statement, or account statement.
    This generates and emails the user's FMPP account statement for the last 12 months.
    Uses a predefined contact number for report generation.
    
    USAGE RULES:
    - Only call when user says: "I want my report", "Send me my statement", "Generate my account statement"
    - Hindi equivalents: "Mujhe report chahiye", "Statement bhejo", "Account statement banao"
    - Do NOT call for general portfolio queries (use transfer_to_portfolio_agent instead)
    - Can only be called ONCE per session
    """
    global _report_requested
    
    # Fixed contact number for report generation
    contact_number = "**********"
    
    # Check if report has already been requested in this session
    if _report_requested:
        app_logger.warning("🚫 Report already requested in this session")
        return "आपके लिए report पहले से ही generate की गई है इस session में। एक session में केवल एक बार report generate कर सकते हैं। नई report के लिए कृपया नया session start करें।"
    
    try:
        app_logger.info(f"📋 Generating report for contact number: {contact_number}")
        
        # Mark that report has been requested
        _report_requested = True
        
        # API endpoint for getting user token
        url = "https://qa-investor-api.lendenclub.com/api/ims/common/v1/get-user-token?mobile_number="
        search_contact = url + contact_number
        
        # Get user token
        response = requests.get(search_contact)
        
        if response.status_code != 200:
            app_logger.error(f"❌ Failed to get user token. Status code: {response.status_code}")
            _report_requested = False  # Reset flag on failure
            return f"आपके contact number {contact_number} के लिए report generate करने में समस्या आई है। कृपया अपना mobile number check करें या customer support से संपर्क करें।"
        
        token_info = response.json().get('data', {})
        access_token = token_info.get('access_token')
        ldc_code = token_info.get('ldc_code')
        
        if not access_token or not ldc_code:
            app_logger.error("❌ Invalid token response")
            _report_requested = False  # Reset flag on failure
            return "आपके account details verify करने में समस्या आई है। कृपया customer support से संपर्क करें।"
        
        # Set up headers for report generation
        headers = {
            "sec-ch-ua-platform": "\"Windows\"",
            "Authorization": f"Token {access_token}",
            "x-ldc-key": ldc_code,
            "Referer": "https://qa-app.lendenclub.com/",
            "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
            "sec-ch-ua-mobile": "?0",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
            "Content-Type": "application/json"
        }
        
        # Parameters for 12-month FMPP account statement
        params = {
            "statement_type": "FMPP_ACCOUNT_STATEMENT",
            "months": "12",
            "send_email": "true"
        }
        
        # Generate report
        report_url = 'https://qa-investor-api.lendenclub.com/api/ims/retail-investor/v5/web/statement-type'
        email_response = requests.get(report_url, headers=headers, params=params)
        
        if email_response.status_code == 200:
            app_logger.info("✅ Report generated successfully")
            return f"✅ आपकी 12-month FMPP account statement successfully generate हो गई है और आपके registered email address पर send कर दी गई है। कृपया अपना email check करें (spam folder भी देखें)। Contact number: {contact_number}"
        else:
            app_logger.error(f"❌ Report generation failed. Status code: {email_response.status_code}")
            _report_requested = False  # Reset flag on failure
            return f"Report generate करने में technical issue आया है। Response code: {email_response.status_code}। कृपया कुछ देर बाद try करें या customer support से संपर्क करें।"
            
    except requests.RequestException as e:
        app_logger.error(f"❌ Network error in get_report: {str(e)}")
        _report_requested = False  # Reset flag on failure
        return "Network connection में समस्या के कारण report generate नहीं हो सकी। कृपया अपना internet connection check करें और फिर से try करें।"
    except Exception as e:
        app_logger.error(f"❌ Unexpected error in get_report: {str(e)}")
        _report_requested = False  # Reset flag on failure
        return "Report generate करने में technical error आया है। कृपया customer support से संपर्क करें।"




@function_tool
async def web_search(
    query: Annotated[
        str,
        "Make query as specific as possible with additional context. "
        "Transform 'what's the weather' to 'what is the weather in [city] "
        "today'. Add relevant constraints, dates, or location details for "
        "better results."
    ]
) -> str:
    """
    Search web for real-time information.
    
    Search for current events, news, weather, stock prices, or recent data 
    not in knowledge base. Called when user asks to search web, google 
    something, or requests current/live information.
    
    Args:
        query: The web search query string.
        
    Returns:
        str: The search result or error message.
    """
    try:
        app_logger.info(f"🌐 Performing web search for: {query}")
        api_key = os.getenv('LINKUP_API_KEY')
        client = LinkupClient(api_key=api_key)
        
        def do_search():
            return client.search(
                query=query,
                depth="standard",
                output_type="sourcedAnswer",
            )
        
        loop = asyncio.get_running_loop()
        response = await loop.run_in_executor(None, do_search)
        
        if hasattr(response, 'answer') and response.answer:
            app_logger.info("Web search completed successfully")
            return response.answer
        else:
            app_logger.warning("Web search returned no direct answer")
            return "No direct answer found. Try rephrasing your question."
    
    except Exception as e:
        app_logger.error(f"Web search error: {e}")
        return "Web search error. Please try again later."


class NeuraVoiceAgent(Agent):
    """
    Main voice agent for P2P lending conversations.
    
    Handles conversations with users, retrieves information from the 
    knowledge base, and answers queries about P2P lending.
    """
    
    def __init__(self, chat_ctx: ChatContext, session_logger: SessionLogger) -> None:
        """
        Initialize the Neura Voice Agent.
        
        Args:
            chat_ctx: The chat context for the conversation.
            session_logger: Session-specific logger instance.
        """
        super().__init__(
            instructions=get_enhanced_system_prompt(),
            chat_ctx=chat_ctx,
            tools=[
                search_knowledge_base, 
                web_search,
                get_report
            ]
        )
        self.session_logger = session_logger

    async def on_enter(self) -> None:
        """Handle agent initialization and greeting."""
        
        self.session_logger.info("Agent entering session - preparing greeting")  
        greeting_instruction = (
            'Greet the user warmly and ask them how you can help them '
            'today about p2p lending.'
        )
        await self.session.generate_reply(instructions=greeting_instruction)
        self.session_logger.info("Initial greeting sent to user")  
        
def prewarm(proc: JobProcess):
        proc.userdata["vad"] = silero.VAD.load()       
   
async def entrypoint(ctx: JobContext):
    # Create session-specific identifier and logger
    session_id = ctx.room.name or f"session_{int(time.time())}"
    session_logger = SessionLogger(session_id)
    
    session_logger.info("🚀 Starting agent entrypoint...")
    
    try:
        # Set log context fields for LiveKit's internal logging
        ctx.log_context_fields = {
            "room": ctx.room.name,
            "session_id": session_id,
        }

        USER_AWAY_TIMEOUT_S = 50.0
        
        session_logger.info(f"Initializing AgentSession with timeout: {USER_AWAY_TIMEOUT_S}s")
        
        session = AgentSession(
            stt=openai.STT(model='gpt-4o-mini-transcribe'),
            vad=ctx.proc.userdata["vad"],
            turn_detection=MultilingualModel(),
            llm=openai.LLM(model='gpt-4o-mini', temperature=0.4, max_completion_tokens =200),
            tts=google.TTS(language='hi-IN',
                          gender='female',
                          voice_name='hi-IN-Chirp3-HD-Laomedeia',
                          sample_rate=24000,
                          speaking_rate=0.95,
                          use_streaming=True,
                          location='asia-southeast1',
                          credentials_file=os.getenv('GOOGLE_APPLICATION_CREDENTIALS')),
           
            allow_interruptions=True,
            user_away_timeout=USER_AWAY_TIMEOUT_S,
        )
        
        session_logger.info("✅ AgentSession created successfully")
        
        # Track inactivity task
        inactivity_task: asyncio.Task | None = None
        
        async def user_presence_task():
            """Check if user is still present after inactivity is detected"""
            session_logger.warning(f"User has been inactive for {USER_AWAY_TIMEOUT_S}s. Starting presence check...")
            
            try:
                # Try to ping the user 3 times, if we get no answer, close the session
                for i in range(3):
                    session_logger.info(f"Performing presence check #{i+1}/3...")
                    await session.generate_reply(
                        instructions="The user has been inactive. Politely check if the user is still present, based on the conversation history. Keep it very short."
                    )
                    await asyncio.sleep(15)
                
                session_logger.warning("User did not respond to presence checks. Ending the session.")
                await asyncio.shield(session.aclose())
                await ctx.room.disconnect()
            except asyncio.CancelledError:
                session_logger.info("User presence check was cancelled - user likely returned")
                # Don't re-raise, this is expected behavior
            except Exception as e:
                session_logger.error(f"Error in user presence check: {e}")
                # Still try to close the session gracefully
                try:
                    await asyncio.shield(session.aclose())
                    await ctx.room.disconnect()
                except Exception as cleanup_error:
                    session_logger.error(f"Error during session cleanup: {cleanup_error}")

        @session.on("user_state_changed")
        def _on_user_state_changed(ev: UserStateChangedEvent):  # noqa: F841
            nonlocal inactivity_task
            session_logger.info(f"User state changed: {ev.old_state} -> {ev.new_state}")
            if ev.new_state == "away":
                inactivity_task = asyncio.create_task(user_presence_task())
                return
            if inactivity_task is not None:
                try:
                    inactivity_task.cancel()
                except Exception as e:
                    session_logger.debug(f"Error cancelling inactivity task: {e}")
                finally:
                    inactivity_task = None

        # log metrics as they are emitted, and total usage after session is over
        usage_collector = metrics.UsageCollector()
        @session.on("metrics_collected")
        def _on_metrics_collected(event: MetricsCollectedEvent):  # noqa: F841
            metrics.log_metrics(event.metrics)
            usage_collector.collect(event.metrics)
            session_logger.debug("Metrics collected and logged")
            
        async def log_usage():
            summary = usage_collector.get_summary()
            session_logger.info(f"Final session usage summary: {summary}")

        session_logger.info("✅ Event handlers configured.")
        
        # shutdown callbacks are triggered when the session is over
        ctx.add_shutdown_callback(log_usage)
        
        bot = NeuraVoiceAgent(chat_ctx=ChatContext(), session_logger=session_logger)
        session_logger.info("✅ NeuraVoiceAgent created")
        
        # Initialize vector DB loader in background
        asyncio.create_task(get_vector_db_loader())
        session_logger.info("Vector DB loader initialization started")
        
        await session.start(
            agent=bot,
            room=ctx.room,
            room_input_options=RoomInputOptions(
                noise_cancellation=noise_cancellation.BVC()
            ),
            room_output_options=RoomOutputOptions(
                transcription_enabled=True,
            )
        )
        
        # Add background ambient audio
        background_audio = BackgroundAudioPlayer(
            ambient_sound=AudioConfig(BuiltinAudioClip.OFFICE_AMBIENCE, volume=0.6),
        )
        await background_audio.start(room=ctx.room, agent_session=session)
        
        session_logger.info("✅ Agent session started with background audio.")
        
    except asyncio.CancelledError:
        session_logger.info("Agent session was cancelled")
        # Don't re-raise, this is expected during shutdown
    except Exception as e:
        session_logger.error(f"Error in entrypoint: {e}")
        raise
    finally:
        session_logger.info("Session entrypoint completed")
    
    
if __name__ == "__main__":
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            # Raise memory warning threshold to reduce noisy alerts.
            job_memory_warn_mb=max(MEMORY_WARN_MB, 1024),
            prewarm_fnc=prewarm,
            # Keep unlimited but warn only when threshold exceeded.
            job_memory_limit_mb=0,
            initialize_process_timeout=25
        )
    )



