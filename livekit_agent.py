"""
Neura Voice P2P Lending Agent

This module provides a voice-enabled agent for P2P lending queries using
LlamaIndex for RAG (Retrieval-Augmented Generation) and vector search.
"""

# Standard library imports
import asyncio
import logging
import os
import sys
import time
from typing import Annotated, Optional

# Third-party imports
from dotenv import load_dotenv
import json
from datetime import datetime
import requests

from prompt import get_enhanced_system_prompt

from linkup import LinkupClient
from clients.retrive import VectorDBLoader

from livekit.plugins.turn_detector.multilingual import MultilingualModel
from livekit.agents import AgentSession,  JobContext, WorkerOptions, cli,  UserStateChangedEvent, metrics, MetricsCollectedEvent, JobProcess, AudioConfig, BackgroundAudioPlayer, BuiltinAudioClip, vad
from livekit.agents.voice import Agent
from livekit.plugins import openai,  noise_cancellation, google, silero
from livekit.agents.llm import ChatContext,  function_tool
from livekit.agents.voice.room_io import RoomInputOptions, RoomOutputOptions
from livekit.agents.metrics import LLMMetrics, STTMetrics, TTSMetrics, EOUMetrics, VADMetrics
import asyncio



# Load environment variables
load_dotenv()

# Constants
LOGS_DIR = "logs"
METRICS_DIR = "metrics"
PERSIST_DIR = "./neura-voice-p2p-engine-storage"
DOCS_DIR = "docs"
QUESTION_COL = "question"
ANSWER_COL = "answer"
BATCH_SIZE = 100
MEMORY_WARN_MB = 1024

# Logging setup
os.makedirs(LOGS_DIR, exist_ok=True)
os.makedirs(METRICS_DIR, exist_ok=True)

# Create a custom filter to reduce verbose external library logs
class ImportantLogsFilter(logging.Filter):
    def filter(self, record):
        # ALWAYS log errors, warnings, and critical messages regardless of source
        if record.levelno >= logging.WARNING:
            return True
            
        # ALWAYS log messages from our main module
        if record.name == '__main__' or 'main_agent' in record.name:
            return True
        
        # Suppress verbose external library logs (only INFO and DEBUG)
        if any(name in record.name for name in [
            'httpcore', 'httpx', 'rustls', 'tungstenite', 'livekit_api',
            'openai._base_client', 'anyio', 'urllib3'
        ]):
            return False  # Suppress INFO/DEBUG from these libraries
        
        # Suppress repetitive VAD metrics (keep only every 10th one)
        if 'Added VAD metrics' in record.getMessage():
            if not hasattr(self, '_vad_counter'):
                self._vad_counter = 0
            self._vad_counter += 1
            return self._vad_counter % 10 == 0
        
        # Suppress repetitive system prompts in HTTP requests
        if 'json_data' in record.getMessage() and len(record.getMessage()) > 1000:
            return False
            
        return True

# Will be set later when session name is available
log_file_path = None

# Initial basic logging setup (will be updated in entrypoint)
logging.basicConfig(
    level=logging.INFO,  # Changed from DEBUG to INFO
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# Add filter to root logger to suppress verbose external logs
root_logger = logging.getLogger()
root_logger.addFilter(ImportantLogsFilter())

# Set up logger for this module
logger = logging.getLogger(__name__)

_vector_db_loader: Optional[VectorDBLoader] = None
_metrics_collector = None

class SessionMetricsCollector:
    """Collects and manages metrics for a single session."""
    
    def __init__(self, session_name: str):
        self.session_name = session_name
        self.session_start_time = datetime.now()
        self.metrics = {
            "session_info": {
                "session_name": session_name,
                "start_time": self.session_start_time.isoformat(),
                "end_time": None,
                "duration_seconds": None
            },
            "structured_conversation": [],
            "usage_summary": None
        }
        self._unmatched_metrics = {
            "stt": {}, "vad": {}, "eou": {}, "llm": [], "tts": {}
        }
        self._unmatched_function_calls = []
        self._current_turn = None
        # Track last turns to attach metrics even when speech_id linking fails
        self._last_user_turn = None  # Points to dict of last user_turn
        self._last_assistant_turn = None  # Points to dict of last assistant_turn
    
    def _get_iso_timestamp(self, ts) -> Optional[str]:
        """Convert a timestamp (datetime, int, float) to an ISO 8601 string."""
        if isinstance(ts, datetime):
            return ts.isoformat()
        if isinstance(ts, (int, float)):
            return datetime.fromtimestamp(ts).isoformat()
        return None

    def _get_turn_for_speech_id(self, speech_id: str, role: str):
        """Finds or creates a turn associated with a speech_id."""
        for turn in reversed(self.metrics["structured_conversation"]):
            turn_data = turn.get(f"{role}_turn")
            if turn_data and turn_data.get("speech_id") == speech_id:
                return turn

        # If no turn has the speech_id, check the current turn
        if self._current_turn:
             turn_data = self._current_turn.get(f"{role}_turn")
             if turn_data and turn_data.get("speech_id") == speech_id:
                return self._current_turn

        return None

    def add_llm_metrics(self, metrics: LLMMetrics):
        """Add LLM metrics to the collection."""
        try:
            metrics_data = {
                "type": metrics.type,
                "label": metrics.label,
                "request_id": metrics.request_id,
                "timestamp": self._get_iso_timestamp(metrics.timestamp),
                "duration": metrics.duration,
                "ttft": metrics.ttft,
                "cancelled": metrics.cancelled,
                "completion_tokens": metrics.completion_tokens,
                "prompt_tokens": metrics.prompt_tokens,
                "total_tokens": metrics.total_tokens,
                "tokens_per_second": metrics.tokens_per_second
            }
            if self._current_turn and 'assistant_turn' in self._current_turn:
                self._current_turn['assistant_turn']['metrics']['llm'].append(metrics_data)
            elif self._last_assistant_turn:
                self._last_assistant_turn['metrics']['llm'].append(metrics_data)
            else:
                self._unmatched_metrics['llm'].append(metrics_data)

            logger.info(f"LLM metrics: tokens={metrics_data.get('total_tokens', 0)}, duration={metrics_data.get('duration', 0):.2f}s")
        except Exception as e:
            logger.error(f"❌ ERROR adding LLM metrics: {e}", exc_info=True)
    
    def add_stt_metrics(self, metrics: STTMetrics):
        """Add STT metrics to the collection."""
        try:
            metrics_data = {
                "type": metrics.type,
                "label": metrics.label,
                "request_id": metrics.request_id,
                "timestamp": self._get_iso_timestamp(metrics.timestamp),
                "duration": metrics.duration,
                "speech_id": metrics.speech_id,
                "error": str(metrics.error) if metrics.error else None,
                "streamed": metrics.streamed,
                "audio_duration": metrics.audio_duration
            }
            
            speech_id = metrics.speech_id
            turn = self._get_turn_for_speech_id(speech_id, 'user')
            if turn:
                turn['user_turn']['metrics']['stt'].append(metrics_data)
            elif self._last_user_turn:
                self._last_user_turn['metrics']['stt'].append(metrics_data)
            else:
                # Store unmatched
                if speech_id not in self._unmatched_metrics['stt']:
                    self._unmatched_metrics['stt'][speech_id] = []
                self._unmatched_metrics['stt'][speech_id].append(metrics_data)
            
            logger.info(f"STT metrics: duration={metrics_data.get('audio_duration', 0):.2f}s")
            if metrics.error:
                logger.error(f"❌ STT ERROR: {metrics.error}")
        except Exception as e:
            logger.error(f"❌ ERROR adding STT metrics: {e}", exc_info=True)
    
    def add_tts_metrics(self, metrics: TTSMetrics):
        """Add TTS metrics to the collection."""
        try:
            metrics_data = {
                "type": metrics.type,
                "label": metrics.label,
                "request_id": metrics.request_id,
                "timestamp": self._get_iso_timestamp(metrics.timestamp),
                "ttfb": metrics.ttfb,
                "duration": metrics.duration,
                "audio_duration": metrics.audio_duration,
                "cancelled": metrics.cancelled,
                "characters_count": metrics.characters_count,
                "streamed": metrics.streamed,
                "speech_id": metrics.speech_id,
                "error": str(getattr(metrics, 'error', None)) if getattr(metrics, 'error', None) else None
            }
            
            speech_id = metrics.speech_id
            turn = self._get_turn_for_speech_id(speech_id, 'assistant')
            if turn:
                turn['assistant_turn']['metrics']['tts'].append(metrics_data)
            elif self._last_assistant_turn:
                self._last_assistant_turn['metrics']['tts'].append(metrics_data)
            else:
                if speech_id not in self._unmatched_metrics['tts']:
                    self._unmatched_metrics['tts'][speech_id] = []
                self._unmatched_metrics['tts'][speech_id].append(metrics_data)

            logger.info(f"TTS metrics: chars={metrics_data.get('characters_count', 0)}, audio_duration={metrics_data.get('audio_duration', 0):.2f}s")
            tts_error = getattr(metrics, 'error', None)
            if tts_error:
                logger.error(f"❌ TTS ERROR: {tts_error}")
        except Exception as e:
            logger.error(f"❌ ERROR adding TTS metrics: {e}", exc_info=True)
    
    def add_eou_metrics(self, metrics: EOUMetrics):
        """Add EOU metrics to the collection."""
        try:
            metrics_data = {
                "type": metrics.type,
                "label": metrics.label,
                "timestamp": self._get_iso_timestamp(metrics.timestamp),
                "end_of_utterance_delay": metrics.end_of_utterance_delay,
                "transcription_delay": metrics.transcription_delay,
                "speech_id": metrics.speech_id,
                "error": str(metrics.error) if metrics.error else None
            }
            speech_id = metrics.speech_id
            turn = self._get_turn_for_speech_id(speech_id, 'user')
            if turn:
                turn['user_turn']['metrics']['eou'].append(metrics_data)
            elif self._last_user_turn:
                self._last_user_turn['metrics']['eou'].append(metrics_data)
            else:
                if speech_id not in self._unmatched_metrics['eou']:
                    self._unmatched_metrics['eou'][speech_id] = []
                self._unmatched_metrics['eou'][speech_id].append(metrics_data)
            
            logger.info(f"EOU metrics: end_of_utterance detected")
            if metrics.error:
                logger.error(f"❌ EOU ERROR: {metrics.error}")
        except Exception as e:
            logger.error(f"❌ ERROR adding EOU metrics: {e}", exc_info=True)
    
    def add_vad_metrics(self, event):
        """Add VAD metrics to the collection."""
        try:
            metrics_data = {
                "type": getattr(event, 'type', 'vad_metrics'),
                "timestamp": self._get_iso_timestamp(event.timestamp),
                "idle_time": getattr(event, 'idle_time', None),
                "inference_duration_total": getattr(event, 'inference_duration_total', None),
                "inference_count": getattr(event, 'inference_count', None),
                "speech_id": getattr(event, 'speech_id', None),
                "error": str(getattr(event, 'error', None)) if getattr(event, 'error', None) else None
            }
            speech_id = getattr(event, 'speech_id', None)
            if speech_id:
                turn = self._get_turn_for_speech_id(speech_id, 'user')
                if turn:
                    turn['user_turn']['metrics']['vad'].append(metrics_data)
                elif self._last_user_turn:
                    self._last_user_turn['metrics']['vad'].append(metrics_data)
                else:
                    if speech_id not in self._unmatched_metrics['vad']:
                        self._unmatched_metrics['vad'][speech_id] = []
                    self._unmatched_metrics['vad'][speech_id].append(metrics_data)

            vad_error = getattr(event, 'error', None)
            if vad_error:
                logger.error(f"❌ VAD ERROR: {vad_error}")
        except Exception as e:
            logger.error(f"❌ ERROR adding VAD metrics: {e}", exc_info=True)
    
    def add_conversation_entry(self, role: str, content: str, speech_id: str = None):
        """Add a conversation entry to the history."""
        if role == 'user':
            if self._current_turn and 'user_turn' not in self._current_turn:
                 # This case should ideally not happen if conversation flows user->assistant
                 self.metrics["structured_conversation"].append(self._current_turn)

            self._current_turn = {
                "user_turn": {
                    "timestamp": datetime.now().isoformat(),
                    "content": content,
                    "speech_id": speech_id,
                    "metrics": {
                        "stt": self._unmatched_metrics['stt'].pop(speech_id, []),
                        "vad": self._unmatched_metrics['vad'].pop(speech_id, []),
                        "eou": self._unmatched_metrics['eou'].pop(speech_id, []),
                    }
                }
            }
            # Remember last user turn (for fallback metric association)
            self._last_user_turn = self._current_turn["user_turn"]
        elif role == 'assistant':
            if not self._current_turn:
                # Assistant starts the conversation (e.g. greeting)
                self._current_turn = {}

            self._current_turn["assistant_turn"] = {
                "timestamp": datetime.now().isoformat(),
                "content": content,
                "speech_id": speech_id,
                "function_calls": self._unmatched_function_calls.copy(),
                "metrics": {
                    "llm": self._unmatched_metrics['llm'].copy(),
                    "tts": self._unmatched_metrics['tts'].pop(speech_id, []),
                }
            }
            # Remember last assistant turn for fallback metric association
            self._last_assistant_turn = self._current_turn["assistant_turn"]
            self.metrics["structured_conversation"].append(self._current_turn)
            self._current_turn = None
            self._unmatched_function_calls = []
            self._unmatched_metrics['llm'] = []


        logger.info(f"Conversation: {role} - {content[:50]}..." if len(content) > 50 else f"Conversation: {role} - {content}")
    
    def add_function_call(self, function_name: str, args: dict, result: str = None):
        """Add a function call to the metrics."""
        entry = {
            "timestamp": datetime.now().isoformat(),
            "function_name": function_name,
            "arguments": args,
            "result": result
        }
        if self._current_turn and 'assistant_turn' in self._current_turn:
             self._current_turn['assistant_turn']['function_calls'].append(entry)
        else:
            self._unmatched_function_calls.append(entry)
        logger.info(f"Function call: {function_name}")
    
    def set_usage_summary(self, summary: str):
        """Set the usage summary for the session."""
        self.metrics["usage_summary"] = summary
    
    def finalize_session(self):
        """Finalize session metrics with end time and duration."""
        if self._current_turn:
            self.metrics['structured_conversation'].append(self._current_turn)
            self._current_turn = None

        end_time = datetime.now()
        self.metrics["session_info"]["end_time"] = end_time.isoformat()
        self.metrics["session_info"]["duration_seconds"] = (end_time - self.session_start_time).total_seconds()
    

    def save_to_json(self):
        """Save metrics to JSON file."""
        try:
            self.finalize_session()
            
            # Clean up speech_id from the final output
            for turn in self.metrics["structured_conversation"]:
                if "user_turn" in turn and turn["user_turn"]:
                    del turn["user_turn"]["speech_id"]
                if "assistant_turn" in turn and turn["assistant_turn"]:
                    del turn["assistant_turn"]["speech_id"]

            filename = f"{self.session_name}_metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            filepath = os.path.join(METRICS_DIR, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.metrics, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ Session metrics saved to: {filepath}")
            
        except Exception as e:
            logger.error(f"❌ Failed to save metrics to JSON: {e}")

async def get_vector_db_loader() -> VectorDBLoader:
    """Lazily initialize VectorDBLoader in a background thread and return the instance."""
    global _vector_db_loader
    if _vector_db_loader is None:
        loop = asyncio.get_running_loop()
        _vector_db_loader = await loop.run_in_executor(None, VectorDBLoader)
    return _vector_db_loader

@function_tool
async def search_knowledge_base(
    query: Annotated[
        str,
        """Convert current user's query into a comprehensive search query,
        optimized for vector similarity matching.
        """
    ]
) -> str:
    """
   
    Searches the internal knowledge base for accurate answers about P2P lending, 
    Lenden club,investment products, services, founders, and other related topics.
     
    This is the **primary and only** source for responding to user queries on these subjects.  
    Use this tool **only for directly relevant questions**, and refer to **conversation history** to 
    avoid redundant lookups of previously fetched information.

    Args:
        query (str): A user question converted into a search-optimized query string.

    Returns:
        str: Formatted Q&A pairs retrieved from the knowledge base.


    """
    global _metrics_collector
    loader = await get_vector_db_loader()
    results = await loader.search(query, top_k=2)
    
    if not results:
        result = "I'm sorry, I couldn't find relevant information in the knowledge base."
    else:
        qa_texts = [f"{idx}. {item.get('formatted_text', '').strip()}" for idx, item in enumerate(results, start=1)]
        result = "\n\n".join(qa_texts)
    
    # Track function call in metrics
    if _metrics_collector:
        _metrics_collector.add_function_call("search_knowledge_base", {"query": query}, result[:200] + "..." if len(result) > 200 else result)
    
    return result



@function_tool
async def web_search(
    query: Annotated[
        str,
        "Make query as specific as possible with additional context. "
        "Transform 'what's the weather' to 'what is the weather in [city] "
        "today'. Add relevant constraints, dates, or location details for "
        "better results."
    ]
) -> str:
    """
    Search web for real-time information.
    
    Search for current events, news, weather, stock prices, or recent data 
    not in knowledge base. Called when user asks to search web, google 
    something, or requests current/live information.
    
    Args:
        query: The web search query string.
        
    Returns:
        str: The search result or error message.
    """
    global _metrics_collector
    try:
        api_key = os.getenv('LINKUP_API_KEY')
        client = LinkupClient(api_key=api_key)
        
        def do_search():
            return client.search(
                query=query,
                depth="standard",
                output_type="sourcedAnswer",
            )
        
        loop = asyncio.get_running_loop()
        response = await loop.run_in_executor(None, do_search)
        
        if hasattr(response, 'answer') and response.answer:
            result = response.answer
        else:
            result = "No direct answer found. Try rephrasing your question."
    
    except Exception as e:
        logger.error(f"Web search error: {e}")
        result = "Web search error. Please try again later."
    
    # Track function call in metrics
    if _metrics_collector:
        _metrics_collector.add_function_call("web_search", {"query": query}, result[:200] + "..." if len(result) > 200 else result)
    
    return result


# Global variable to track report requests across sessions
_report_requested_global = False

@function_tool
async def get_report_with_verification(
    last_four_digits: Annotated[
        str,
        "The last 4 digits of the user's registered mobile number for verification."
    ]
) -> str:
    """
    ONLY call this function when the user explicitly asks for their report, statement, or account statement.
    This generates and emails the user's FMPP account statement for the last 12 months.
    Requires verification of last 4 digits of registered mobile number, ask the user for this before calling the function.  
    """
    global _report_requested_global
    
    # List of registered phone numbers (can be expanded later)
    registered_numbers = ["**********"]
    
    # Check if report has already been requested in this session
    if _report_requested_global:
        logger.warning("🚫 Report already requested in this session")
        return ("Your statement for this session has already been generated. "
                "You can only request one statement per session")
    
    # Function to convert Hindi digits to English
    def convert_hindi_to_english(text):
        hindi_to_english = {
            '०': '0', '१': '1', '२': '2', '३': '3', '४': '4',
            '५': '5', '६': '6', '७': '7', '८': '8', '९': '9'
        }
        result = text
        for hindi, english in hindi_to_english.items():
            result = result.replace(hindi, english)
        return result
    
    # Extract and clean the last 4 digits
    def extract_digits(text):
        text = convert_hindi_to_english(text)
        digits = ''.join(filter(str.isdigit, text))
        return digits
    
    try:
        logger.info("📋 Verifying last 4 digits for report generation")
        input_digits = extract_digits(last_four_digits)
        
        if len(input_digits) < 4:
            logger.warning(f"🚫 Insufficient digits provided: {input_digits}")
            return ("Please provide the complete four digits of your registered mobile number. "
                   "For example, if your number ends in 4566, you would provide '4566'.")
        
        # Take last 4 digits if more than 4 provided
        user_last_four = input_digits[-4:]
        logger.info(f"🔍 User provided last 4 digits: {user_last_four}")
        
        # Check if these digits match any registered number
        matched_number = None
        for number in registered_numbers:
            if number.endswith(user_last_four):
                matched_number = number
                break
        
        if not matched_number:
            logger.warning(f"🚫 No matching number found for digits: {user_last_four}")
            return (f"The last 4 digits you provided ({user_last_four}) do not match our records. "
                    "Please confirm you are using the correct registered mobile number.")
        
        logger.info(f"✅ Phone number verified: {matched_number}")
        
        # Mark that report has been requested
        _report_requested_global = True
        contact_number = matched_number
        
        # API endpoint for getting user token
        url = "https://qa-investor-api.lendenclub.com/api/ims/common/v1/get-user-token?mobile_number="
        response = requests.get(url + contact_number)
        
        if response.status_code != 200:
            logger.error(f"❌ Failed to get user token. Status code: {response.status_code}")
            _report_requested_global = False
            return (f"There was an issue generating the report for contact number {contact_number}. "
                    "Please check your mobile number or contact customer support.")
        
        token_info = response.json().get('data', {})
        access_token = token_info.get('access_token')
        ldc_code = token_info.get('ldc_code')
        
        if not access_token or not ldc_code:
            logger.error("❌ Invalid token response")
            _report_requested_global = False
            return "There was an issue verifying your account details. Please contact customer support."
        
        headers = {
            "sec-ch-ua-platform": "\"Windows\"",
            "Authorization": f"Token {access_token}",
            "x-ldc-key": ldc_code,
            "Referer": "https://qa-app.lendenclub.com/",
            "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
            "sec-ch-ua-mobile": "?0",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
            "Content-Type": "application/json"
        }
        
        params = {
            "statement_type": "FMPP_ACCOUNT_STATEMENT",
            "months": "12",
            "send_email": "true"
        }
        
        report_url = "https://qa-investor-api.lendenclub.com/api/ims/retail-investor/v5/web/statement-type"
        email_response = requests.get(report_url, headers=headers, params=params)
        
        if email_response.status_code == 200:
            logger.info("✅ Report generated successfully")
            return (f"✅ आपकी 12-month FMPP account statement successfully generate हो गई है और आपके "
                    f"registered email address पर send कर दी गई है। कृपया अपना email check करें "
                    f"(spam folder भी देखें)। Contact number: {contact_number}")
        else:
            logger.error(f"❌ Report generation failed. Status code: {email_response.status_code}")
            _report_requested_global = False
            return (f"Report generate करने में technical issue आया है। Response code: {email_response.status_code}। "
                    "कृपया कुछ देर बाद try करें या customer support से संपर्क करें।")
    
    except requests.RequestException as e:
        logger.error(f"❌ Network error in get_report: {str(e)}")
        _report_requested_global = False
        return "Network connection में समस्या के कारण report generate नहीं हो सकी। कृपया अपना internet connection check करें और फिर से try करें।"
    except Exception as e:
        logger.error(f"❌ Unexpected error in get_report: {str(e)}")
        _report_requested_global = False
        return "Report generate करने में technical error आया है। कृपया customer support से संपर्क करें।"


class NeuraVoiceAgent(Agent):
    """
    Main voice agent for P2P lending conversations.
    
    Handles conversations with users, retrieves information from the 
    knowledge base, and answers queries about P2P lending.
    """
    
    def __init__(self, metrics_collector: SessionMetricsCollector) -> None:
        """
        Initialize the Neura Voice Agent.
        
        Args:
            metrics_collector: The metrics collector for this session.
        """
        # Initialize components
        logger.info("🔧 Initializing STT component in Agent...")
        stt = openai.STT(model='gpt-4o-mini-transcribe')
        logger.info("🔧 Initializing LLM component in Agent...")
        llm = openai.LLM(model='gpt-4o-mini', temperature=0.6)
        logger.info("🔧 Initializing TTS component in Agent...")
        tts = google.TTS(language='hi-IN',
                        gender='female',
                        voice_name='hi-IN-Chirp3-HD-Laomedeia',
                        sample_rate=24000,
                        speaking_rate=0.95,
                        use_streaming=True,
                        location='asia-southeast1',
                        credentials_file=os.getenv('GOOGLE_APPLICATION_CREDENTIALS'))
        logger.info("🔧 Initializing VAD component in Agent...")
        silero_vad = silero.VAD.load()

        # Initialize parent Agent with tool functions
        super().__init__(
            instructions=get_enhanced_system_prompt(),
            stt=stt,
            llm=llm,
            tts=tts,
            vad=silero_vad,
            tools=[
                search_knowledge_base,
                web_search,
                get_report_with_verification
            ]
        )
        self.metrics_collector = metrics_collector
        
        # Set up event handlers for all metric types
        logger.info("🔧 Setting up LLM metrics handler in Agent...")
        def llm_metrics_wrapper(metrics: LLMMetrics):
            asyncio.create_task(self.on_llm_metrics_collected(metrics))
        llm.on("metrics_collected", llm_metrics_wrapper)
        
        logger.info("🔧 Setting up STT metrics handler in Agent...")
        def stt_metrics_wrapper(metrics: STTMetrics):
            asyncio.create_task(self.on_stt_metrics_collected(metrics))
        stt.on("metrics_collected", stt_metrics_wrapper)
        
        logger.info("🔧 Setting up EOU metrics handler in Agent...")
        def eou_metrics_wrapper(metrics: EOUMetrics):
            asyncio.create_task(self.on_eou_metrics_collected(metrics))
        stt.on("eou_metrics_collected", eou_metrics_wrapper)
        
        logger.info("🔧 Setting up TTS metrics handler in Agent...")
        def tts_metrics_wrapper(metrics: TTSMetrics):
            asyncio.create_task(self.on_tts_metrics_collected(metrics))
        tts.on("metrics_collected", tts_metrics_wrapper)
        
        logger.info("🔧 Setting up VAD metrics handler in Agent...")
        def vad_metrics_wrapper(event):
            asyncio.create_task(self.on_vad_event(event))
        silero_vad.on("metrics_collected", vad_metrics_wrapper)

    async def on_llm_metrics_collected(self, metrics: LLMMetrics) -> None:
        """Handle LLM metrics collection"""
        self.metrics_collector.add_llm_metrics(metrics)

    async def on_stt_metrics_collected(self, metrics: STTMetrics) -> None:
        """Handle STT metrics collection"""
        self.metrics_collector.add_stt_metrics(metrics)

    async def on_eou_metrics_collected(self, metrics: EOUMetrics) -> None:
        """Handle EOU metrics collection"""
        self.metrics_collector.add_eou_metrics(metrics)

    async def on_tts_metrics_collected(self, metrics: TTSMetrics) -> None:
        """Handle TTS metrics collection"""
        self.metrics_collector.add_tts_metrics(metrics)

    async def on_vad_event(self, event) -> None:
        """Handle VAD event collection"""
        self.metrics_collector.add_vad_metrics(event)

    async def on_enter(self) -> None:
        """Handle agent initialization and greeting."""
        greeting_instruction = (
            "Greet the user warmly and ask them how you can help them "
            "today about p2p lending."
        )
        self.metrics_collector.add_conversation_entry("system", greeting_instruction)
        
        # Generate the greeting and try to capture the response
        try:
            await self.session.generate_reply(instructions=greeting_instruction)
            
            # The response should be captured by the event handlers, but let's log it here too as fallback
            logger.info("📝 [GREETING] Agent greeting generated successfully")
        except Exception as e:
            logger.error(f"Error generating greeting: {e}")

        
def prewarm(proc: JobProcess):
        # Preload VAD for faster initialization
        pass   

   
async def entrypoint(ctx: JobContext):
    logger.info("🚀 Starting agent entrypoint...")
    try:
        
        ctx.log_context_fields = {
        "room": ctx.room.name,
        }

        # Create session metrics collector
        session_name = ctx.room.name or f"session_{int(time.time())}"
        metrics_collector = SessionMetricsCollector(session_name)
        
        # Set up session-specific log file
        global log_file_path
        log_file_path = os.path.join(LOGS_DIR, f"{session_name}.log")
        
        # Add file handler for session-specific logging
        file_handler = logging.FileHandler(log_file_path)
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(name)s - %(message)s'))
        file_handler.addFilter(ImportantLogsFilter())
        root_logger = logging.getLogger()
        root_logger.addHandler(file_handler)
        
        logger.info(f"📊 Created metrics collector for session: {session_name}")
        logger.info(f"📝 Session logs will be saved to: {log_file_path}")
        
        # Set global metrics collector for function tools
        global _metrics_collector
        _metrics_collector = metrics_collector

        USER_AWAY_TIMEOUT_S = 30.0
        
        # Create the agent with metrics collector
        bot = NeuraVoiceAgent(metrics_collector=metrics_collector)
        logger.info("✅ NeuraVoiceAgent created with metrics collection")
        
        session = AgentSession()
        # logger.info(f"✅ AgentSession created with user_away_timeout of {USER_AWAY_TIMEOUT_S}s.")
        
        # Track inactivity task
        inactivity_task: asyncio.Task | None = None
        
        async def user_presence_task():
            """Check if user is still present after inactivity is detected"""
            logger.warning(f"User has been inactive for {USER_AWAY_TIMEOUT_S}s. Starting presence check...")
            
            try:
                # Try to ping the user 3 times, if we get no answer, close the session
                for i in range(3):
                    logger.info(f"Performing presence check #{i+1}/3...")
                    await session.generate_reply(
                        instructions="The user has been inactive. Politely check if the user is still present, based on the conversation history. Keep it very short."
                    )
                    await asyncio.sleep(15)
                
                logger.warning("User did not respond to presence checks. Ending the session.")
                await asyncio.shield(session.aclose())
                # Save metrics before disconnecting
                await save_metrics()
                await ctx.room.disconnect()
            except asyncio.CancelledError:
                logger.info("User presence check was cancelled - user likely returned")
                # Don't re-raise, this is expected behavior
            except Exception as e:
                logger.error(f"Error in user presence check: {e}")
                # Still try to close the session gracefully
                try:
                    await asyncio.shield(session.aclose())
                    # Save metrics before disconnecting
                    await save_metrics()
                    await ctx.room.disconnect()
                except Exception as cleanup_error:
                    logger.error(f"Error during session cleanup: {cleanup_error}")

        @session.on("user_state_changed")
        def _on_user_state_changed(ev: UserStateChangedEvent):  # noqa: F841
            nonlocal inactivity_task
            if ev.new_state == "away":
                inactivity_task = asyncio.create_task(user_presence_task())
                return
            if inactivity_task is not None:
                try:
                    inactivity_task.cancel()
                except Exception as e:
                    logger.debug(f"Error cancelling inactivity task: {e}")
                finally:
                    inactivity_task = None

        # log metrics as they are emitted, and total usage after session is over
        usage_collector = metrics.UsageCollector()
        @session.on("metrics_collected")
        def _on_metrics_collected(event: MetricsCollectedEvent):  # noqa: F841
            metrics.log_metrics(event.metrics)
            usage_collector.collect(event.metrics)
        
        # Simplified, efficient conversation tracking
        conversation_logged = set()  # Prevent duplicates

        @session.on("conversation_item_added")
        def _on_conversation_item_added(event):
            try:
                item = getattr(event, "item", None)
                if not item:
                    return
                
                role = getattr(item, "role", "")
                content = _extract_text_content(getattr(item, "content", []))
                speech_id = getattr(item, "speech_id", None)
                
                if content and role in ["user", "assistant"]:
                    # Prevent duplicate logging
                    entry_hash = hash((role, content[:100], speech_id))
                    if entry_hash not in conversation_logged:
                        conversation_logged.add(entry_hash)
                        metrics_collector.add_conversation_entry(role, content, speech_id)
                        logger.info(f"📝 {role.title()}: {content[:50]}...")
                    
            except Exception as e:
                logger.error(f"Conversation logging error: {e}")

        def _extract_text_content(content):
            """Optimized text extraction helper."""
            if isinstance(content, str):
                return content.strip()
            elif isinstance(content, list):
                return "".join(str(part.get("text", part) if isinstance(part, dict) 
                                  else getattr(part, "text", str(part))) for part in content).strip()
            return str(content).strip() if content else ""

        # Remove redundant fallback handlers - keep only one as backup
        @session.on("user_speech_committed") 
        def _fallback_user_speech(event):
            transcript = getattr(event, 'transcript', None)
            speech_id = getattr(event, 'speech_id', None)
            if transcript and transcript.strip():
                entry_hash = hash(("user", transcript[:100], speech_id))
                if entry_hash not in conversation_logged:
                    conversation_logged.add(entry_hash)
                    metrics_collector.add_conversation_entry("user", transcript.strip(), speech_id)
        
        # Log only important session events (reduced verbosity)
        original_emit = session.emit
        def debug_emit(event_name, *args, **kwargs):
            # Only log key events, not all debug events
            if event_name in ['user_speech_committed', 'agent_speech_committed', 'user_message', 'agent_message']:
                logger.info(f"🔍 Session event: {event_name}")
                if args and hasattr(args[0], '__dict__'):
                    event_obj = args[0]
                    # Log only essential attributes, not verbose content
                    if hasattr(event_obj, 'role'):
                        logger.info(f"   Role: {event_obj.role}")
                    if hasattr(event_obj, 'content') and len(str(event_obj.content)) < 200:
                        logger.info(f"   Content: {str(event_obj.content)[:100]}...")
            return original_emit(event_name, *args, **kwargs)
        session.emit = debug_emit
            
        async def log_usage():
            summary = usage_collector.get_summary()
            logger.info(f"Usage: {summary}")
            # Add usage summary to metrics
            metrics_collector.set_usage_summary(str(summary))

        async def save_metrics():
            """Save session metrics to JSON file."""
            await asyncio.to_thread(metrics_collector.save_to_json)


        logger.info("✅ Event handlers configured.")
        
        # shutdown callbacks are triggered when the session is over
        ctx.add_shutdown_callback(log_usage)
        ctx.add_shutdown_callback(save_metrics)
        
        asyncio.create_task(get_vector_db_loader())
        
        await session.start(
            agent=bot,
            room=ctx.room,
            room_input_options=RoomInputOptions(
                noise_cancellation=noise_cancellation.BVC()
            ),
            room_output_options=RoomOutputOptions(
                transcription_enabled=True,
            )
        )
        
        # Add background ambient audio
        background_audio = BackgroundAudioPlayer(
            ambient_sound=AudioConfig(BuiltinAudioClip.OFFICE_AMBIENCE, volume=0.6),
        )
        await background_audio.start(room=ctx.room, agent_session=session)
        
        logger.info("✅ Agent session started with background audio.")
        
    except asyncio.CancelledError:
        logger.info("Agent session was cancelled")
        # Don't re-raise, this is expected during shutdown
    except Exception as e:
        logger.error(f"❌ CRITICAL ERROR in entrypoint: {e}", exc_info=True)
        raise  # Re-raise to ensure the error is properly handled
    
    
if __name__ == "__main__":
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            # Raise memory warning threshold to reduce noisy alerts.
            job_memory_warn_mb=max(MEMORY_WARN_MB, 1024),
            prewarm_fnc=prewarm,
            # Keep unlimited but warn only when threshold exceeded.
            job_memory_limit_mb=0,
            initialize_process_timeout=25
        )
    )


