{"session_info": {"session_name": "playground-koAL-HxcJ", "start_time": "2025-08-02T05:58:52.894024", "end_time": "2025-08-02T05:59:26.794983", "duration_seconds": 33.900959}, "llm_metrics": [], "stt_metrics": [], "tts_metrics": [], "eou_metrics": [], "vad_metrics": [{"type": "vad_metrics", "timestamp": 1754114336.9647417, "idle_time": 3.9267662790007307, "inference_duration_total": 0.02384481499029789, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114336.9655461, "idle_time": 3.9268674750001082, "inference_duration_total": 0.024013860000195564, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114337.9635632, "idle_time": 4.925587642999744, "inference_duration_total": 0.019361868002306437, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114337.9640234, "idle_time": 4.925344175999271, "inference_duration_total": 0.019591597996623022, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114339.0128312, "idle_time": 5.974855735999881, "inference_duration_total": 0.017978994990698993, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114339.0132885, "idle_time": 5.9746095649989, "inference_duration_total": 0.017987729004744324, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114340.0132155, "idle_time": 6.975239896999483, "inference_duration_total": 0.016713181999875815, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114340.0136557, "idle_time": 6.9749764099997265, "inference_duration_total": 0.0163441199947556, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114341.0631962, "idle_time": 8.025220620000255, "inference_duration_total": 0.017830642997068935, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114341.0636685, "idle_time": 8.024989427000037, "inference_duration_total": 0.017631132001042715, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114342.0632865, "idle_time": 9.025310823999462, "inference_duration_total": 0.017144155002824846, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114342.0637887, "idle_time": 9.025109624999459, "inference_duration_total": 0.017482458999438677, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114343.1126862, "idle_time": 10.07471047800027, "inference_duration_total": 0.019118921993140248, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114343.1131294, "idle_time": 10.074450537998928, "inference_duration_total": 0.018401782999717398, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114344.1137643, "idle_time": 11.07578864199968, "inference_duration_total": 0.01686467300351069, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114344.1142201, "idle_time": 11.075540998999713, "inference_duration_total": 0.017179158001454198, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114345.162763, "idle_time": 12.124787258000651, "inference_duration_total": 0.016527100999155664, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114345.1633346, "idle_time": 12.124655702999007, "inference_duration_total": 0.016865998000866966, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114346.1632361, "idle_time": 13.125260393000644, "inference_duration_total": 0.016989419998935773, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114346.163693, "idle_time": 13.125013909999325, "inference_duration_total": 0.016703865007002605, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114347.2133148, "idle_time": 14.175339167999482, "inference_duration_total": 0.018524299999626237, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114347.2139134, "idle_time": 14.175234373999047, "inference_duration_total": 0.018932798002424533, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114348.2137992, "idle_time": 15.175823453999328, "inference_duration_total": 0.018736797994279186, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114348.2141743, "idle_time": 15.175495306999437, "inference_duration_total": 0.01860264600145456, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114349.2630339, "idle_time": 16.225058144000286, "inference_duration_total": 0.017171934003272327, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114349.263615, "idle_time": 16.22493584899894, "inference_duration_total": 0.01781319699875894, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114350.2637143, "idle_time": 17.22573880299933, "inference_duration_total": 0.020248644996172516, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114350.2642384, "idle_time": 17.225559185999373, "inference_duration_total": 0.020347588000731776, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114351.3131003, "idle_time": 18.274421227999483, "inference_duration_total": 0.020734907002406544, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114351.3132906, "idle_time": 18.27531475700016, "inference_duration_total": 0.020847941996180452, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114352.31441, "idle_time": 19.276434282999617, "inference_duration_total": 0.01993716199467599, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114352.3149743, "idle_time": 19.276295092999135, "inference_duration_total": 0.01952959000118426, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114353.364098, "idle_time": 20.32612248300029, "inference_duration_total": 0.023359077000350226, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114353.364372, "idle_time": 20.325692966000133, "inference_duration_total": 0.02266211599635426, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114354.363864, "idle_time": 21.325888336999924, "inference_duration_total": 0.019432688010056154, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114354.364344, "idle_time": 21.325664838999728, "inference_duration_total": 0.01914184100314742, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114355.4135594, "idle_time": 22.3755838699999, "inference_duration_total": 0.02063997899495007, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114355.4142048, "idle_time": 22.375525976000063, "inference_duration_total": 0.022929295993890264, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114356.4131632, "idle_time": 23.375187662000826, "inference_duration_total": 0.019990115995824453, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114356.4134438, "idle_time": 23.374764729998788, "inference_duration_total": 0.01839325400214875, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114357.4627228, "idle_time": 24.424747110999306, "inference_duration_total": 0.017523796997920726, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114357.463338, "idle_time": 24.42465901399919, "inference_duration_total": 0.01825867500338063, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114358.4631095, "idle_time": 25.425133626000388, "inference_duration_total": 0.01767329299764242, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114358.46328, "idle_time": 25.424600742999246, "inference_duration_total": 0.017533995007397607, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114359.5131762, "idle_time": 26.475200713999584, "inference_duration_total": 0.017263559997445554, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114359.513533, "idle_time": 26.47485418999895, "inference_duration_total": 0.01714335899305297, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114360.5132449, "idle_time": 27.474566027000037, "inference_duration_total": 0.019655500998851494, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114360.5135121, "idle_time": 27.47553660400081, "inference_duration_total": 0.020521465994534083, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114361.5634124, "idle_time": 28.525436886000534, "inference_duration_total": 0.020408097996551078, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114361.5640216, "idle_time": 28.525342494998768, "inference_duration_total": 0.02068358500218892, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114362.5632727, "idle_time": 29.525297131000116, "inference_duration_total": 0.020844640990617336, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114362.5637176, "idle_time": 29.525038474999747, "inference_duration_total": 0.019671020008900086, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114363.5633981, "idle_time": 30.525422549000723, "inference_duration_total": 0.01565951100201346, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114363.563854, "idle_time": 30.525174964999678, "inference_duration_total": 0.016240643008131883, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114364.612591, "idle_time": 31.574615416999222, "inference_duration_total": 0.01937431900660158, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114364.6130357, "idle_time": 31.57435677399917, "inference_duration_total": 0.01842864699756319, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114365.614191, "idle_time": 32.57621561999986, "inference_duration_total": 0.019211969996831613, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114365.614823, "idle_time": 32.57614419899983, "inference_duration_total": 0.019323099999382976, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114366.6640742, "idle_time": 33.62609846899977, "inference_duration_total": 0.02228817599825561, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754114366.6646104, "idle_time": 33.62593117999859, "inference_duration_total": 0.022286417994109797, "inference_count": 32, "speech_id": null, "error": null}], "conversation_history": [{"timestamp": "2025-08-02T05:58:53.038116", "role": "system", "content": "Greet the user warmly and ask them how you can help them today about p2p lending."}], "function_calls": [], "usage_summary": "UsageSummary(llm_prompt_tokens=0, llm_prompt_cached_tokens=0, llm_completion_tokens=0, tts_characters_count=0, tts_audio_duration=0.0, stt_audio_duration=0.0)"}