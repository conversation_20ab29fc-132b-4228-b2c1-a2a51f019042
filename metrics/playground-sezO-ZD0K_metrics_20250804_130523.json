{"session_info": {"session_name": "playground-sezO-ZD0K", "start_time": "2025-08-04T13:04:13.102283", "end_time": "2025-08-04T13:05:23.667226", "duration_seconds": 70.564943}, "structured_conversation": [{"user_turn": {"timestamp": null, "content": "", "metrics": {"stt": [], "vad": [{"type": "vad_metrics", "timestamp": 1754312655.0100632, "idle_time": 1.790611649004859, "inference_duration_total": 0.024713391016121022, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312655.0108407, "idle_time": 1.789767098001903, "inference_duration_total": 0.026154732979193795, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312656.0100522, "idle_time": 2.790600591004477, "inference_duration_total": 0.024420356006885413, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312656.010478, "idle_time": 2.7894043560008868, "inference_duration_total": 0.024170286997104995, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312657.0601697, "idle_time": 3.840718289000506, "inference_duration_total": 0.018625596960191615, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312657.0606358, "idle_time": 3.8395623120013624, "inference_duration_total": 0.01953473797038896, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312658.059802, "idle_time": 4.840350559003127, "inference_duration_total": 0.018925581018265802, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312658.0602453, "idle_time": 4.83917164200102, "inference_duration_total": 0.020573127003444824, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312659.1102402, "idle_time": 5.890788631004398, "inference_duration_total": 0.01693471297767246, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312659.1107018, "idle_time": 5.8896282499990775, "inference_duration_total": 0.017492406004748773, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312660.113565, "idle_time": 6.894113367998216, "inference_duration_total": 0.02333856398036005, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312660.1140542, "idle_time": 6.892980651995458, "inference_duration_total": 0.02353106600639876, "inference_count": 32, "speech_id": null, "error": null}], "eou": []}}, "assistant_turn": {"timestamp": "2025-08-04T13:04:21.012327", "content": "Hello! Aap kaise hain? Main <PERSON><PERSON><PERSON> hoon, aur main aapki madad karne ke liye yahan hoon. Aapko P2P lending", "function_calls": [], "metrics": {"llm": [{"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C0p9JLgTFAuVBrNA36nEzPrJZ6jcB", "timestamp": 1754312655.2712367, "duration": 2.0491969180002343, "ttft": 1.092570964996412, "cancelled": false, "completion_tokens": 54, "prompt_tokens": 860, "total_tokens": 914, "tokens_per_second": 26.351786656353845}], "tts": [{"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "dcc349a67669", "timestamp": 1754312655.9177988, "ttfb": 0.34106045099906623, "duration": 1.4552231350025977, "audio_duration": 8.607333333333335, "cancelled": false, "characters_count": 151, "streamed": true, "speech_id": "speech_89bd2d58b938", "error": null}]}}}, {"user_turn": {"timestamp": "2025-08-04T13:04:22.155932", "content": "<PERSON><PERSON><PERSON>", "metrics": {"stt": [], "vad": [{"type": "vad_metrics", "timestamp": 1754312661.1605427, "idle_time": 0.5978823120021843, "inference_duration_total": 0.02680061000137357, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312661.161022, "idle_time": 0.5978325910036801, "inference_duration_total": 0.025336683997011278, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312662.1616027, "idle_time": 0.9507377269983408, "inference_duration_total": 0.026660614043066744, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312662.1620197, "idle_time": 0.9509163280017674, "inference_duration_total": 0.02923495802679099, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312663.2105198, "idle_time": 1.9996546620022855, "inference_duration_total": 0.02133755297109019, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312663.2107046, "idle_time": 1.9996011680050287, "inference_duration_total": 0.022885966034664307, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312664.218486, "idle_time": 3.0076209410035517, "inference_duration_total": 0.03360849899036111, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312664.219459, "idle_time": 3.008355459001905, "inference_duration_total": 0.035168444002920296, "inference_count": 32, "speech_id": null, "error": null}], "eou": []}}, "assistant_turn": {"timestamp": "2025-08-04T13:04:24.710268", "content": "<PERSON><PERSON><PERSON> is baare mein", "function_calls": [], "metrics": {"llm": [{"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C0p9S84mYNhHtp9mS6wPyVaE5x35q", "timestamp": 1754312663.620142, "duration": 1.4690763789985795, "ttft": 0.7136818669969216, "cancelled": false, "completion_tokens": 40, "prompt_tokens": 897, "total_tokens": 937, "tokens_per_second": 27.22799207163529}], "tts": [{"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "2f3a7a2716db", "timestamp": 1754312664.440056, "ttfb": 0.28225762900547124, "duration": 1.3317773210001178, "audio_duration": 8.067333333333337, "cancelled": false, "characters_count": 143, "streamed": true, "speech_id": "speech_80b2836857e2", "error": null}]}}}, {"user_turn": {"timestamp": "2025-08-04T13:04:30.155502", "content": "We need I was asking about founders.", "metrics": {"stt": [], "vad": [{"type": "vad_metrics", "timestamp": 1754312665.3269866, "idle_time": 0.6159324920008658, "inference_duration_total": 0.023988428991287947, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312665.3274763, "idle_time": 0.6175870939987362, "inference_duration_total": 0.023808956000721082, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312666.2627864, "idle_time": 0.8030503920017509, "inference_duration_total": 0.023237080007675104, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312666.2633712, "idle_time": 0.8033197729964741, "inference_duration_total": 0.02338355499523459, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312669.5537248, "idle_time": 2.4433765959984157, "inference_duration_total": 0.02911315797973657, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312669.5548685, "idle_time": 2.4441249939991394, "inference_duration_total": 0.03035236901632743, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312669.5825865, "idle_time": 2.4722387779984274, "inference_duration_total": 0.02473480999469757, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312669.5842268, "idle_time": 2.473483469999337, "inference_duration_total": 0.025977038996643387, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312669.606242, "idle_time": 2.4958940930009703, "inference_duration_total": 0.02083271401352249, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312669.610156, "idle_time": 2.499412589997519, "inference_duration_total": 0.023277227002836298, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312670.3639386, "idle_time": 3.253590471998905, "inference_duration_total": 0.025181370008795056, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312670.3643878, "idle_time": 3.2536444060024223, "inference_duration_total": 0.023121822989196517, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312671.4131129, "idle_time": 4.30276496100123, "inference_duration_total": 0.02617087701219134, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312671.4137268, "idle_time": 4.302983398003562, "inference_duration_total": 0.024937173999205697, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312672.4153144, "idle_time": 5.304966413001239, "inference_duration_total": 0.02399084502394544, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312672.4157379, "idle_time": 5.304994486003125, "inference_duration_total": 0.023363352011074312, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312673.459833, "idle_time": 6.349484839003708, "inference_duration_total": 0.020727010029077064, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312673.4604723, "idle_time": 6.349728909997793, "inference_duration_total": 0.020664178009610623, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312674.4597397, "idle_time": 7.349391572999593, "inference_duration_total": 0.021213368039752822, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312674.4601905, "idle_time": 7.349446975000319, "inference_duration_total": 0.019806106021860614, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312675.5124831, "idle_time": 8.402135103999171, "inference_duration_total": 0.019485063989122864, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312675.5131164, "idle_time": 8.40237296900159, "inference_duration_total": 0.02433955699962098, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312676.5099711, "idle_time": 9.399623203004012, "inference_duration_total": 0.022628063001320697, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312676.5104475, "idle_time": 9.399704162002308, "inference_duration_total": 0.021524240997678135, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312677.5605388, "idle_time": 10.45019096499891, "inference_duration_total": 0.023141628029407002, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312677.5611715, "idle_time": 10.450428103999002, "inference_duration_total": 0.02229024202824803, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312678.5602407, "idle_time": 11.449497319001239, "inference_duration_total": 0.019468872000288684, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312678.5604079, "idle_time": 11.450059748000058, "inference_duration_total": 0.01984302898927126, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312679.6094797, "idle_time": 12.499131636002858, "inference_duration_total": 0.016954435013758484, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312679.609824, "idle_time": 12.499080519002746, "inference_duration_total": 0.017822736030211672, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312680.6100135, "idle_time": 13.49966551399848, "inference_duration_total": 0.017842399960500188, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312680.6104698, "idle_time": 13.499726460002421, "inference_duration_total": 0.018330961036554072, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312681.6103647, "idle_time": 14.500016689002223, "inference_duration_total": 0.01754195796820568, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312681.610843, "idle_time": 14.500099381999462, "inference_duration_total": 0.018363992996455636, "inference_count": 32, "speech_id": null, "error": null}], "eou": []}}, "assistant_turn": {"timestamp": "2025-08-04T13:04:42.657363", "content": "लेन-देन Club ke founders <PERSON><PERSON><PERSON> aur <PERSON> hain. Iska establishment 2015 mein hua tha. Aapko aur koi information chahiye?", "function_calls": [{"timestamp": "2025-08-04T13:04:32.406224", "function_name": "search_knowledge_base", "arguments": {"query": "founders of लेन-देन Club"}, "result": "1. 📌 Question: Who are the founders of LenDenClub?\n💡 Answer: The founders of LenDenClub are <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.\n\n2. 📌 Question: Who founded LenDenClub and in which year?\n💡 Answer: LenDenClu..."}], "metrics": {"llm": [{"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C0p9apZFP0GvAV2UbWAa4oUzmsEPE", "timestamp": 1754312671.3156974, "duration": 1.1668366150042857, "ttft": 1.1644238709995989, "cancelled": false, "completion_tokens": 23, "prompt_tokens": 920, "total_tokens": 943, "tokens_per_second": 19.711414352484578}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C0p9cLTU7swmOCfJLAHX6ZAi0vtq6", "timestamp": 1754312674.3248415, "duration": 1.9175060460038367, "ttft": 0.8454184440051904, "cancelled": false, "completion_tokens": 38, "prompt_tokens": 1027, "total_tokens": 1065, "tokens_per_second": 19.81740817933461}], "tts": [{"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "325bfb84a2c9", "timestamp": 1754312675.5334055, "ttfb": 0.7569633640014217, "duration": 1.581223637003859, "audio_duration": 7.947333333333337, "cancelled": false, "characters_count": 133, "streamed": true, "speech_id": "speech_c7b286d5f790", "error": null}]}}}, {"user_turn": {"timestamp": "2025-08-04T13:04:48.266502", "content": "Nice. Tell me about manual lending.", "metrics": {"stt": [], "vad": [{"type": "vad_metrics", "timestamp": 1754312682.6634135, "idle_time": 15.553065558997332, "inference_duration_total": 0.016367270982300397, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312682.6638846, "idle_time": 15.55314122600248, "inference_duration_total": 0.017216852014826145, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312683.662461, "idle_time": 0.7502523799994378, "inference_duration_total": 0.02087509798730025, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312683.6629374, "idle_time": 0.7507442610003636, "inference_duration_total": 0.020705746988824103, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312684.7119255, "idle_time": 1.799717112997314, "inference_duration_total": 0.02090411000244785, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312684.7124166, "idle_time": 1.8002236409956822, "inference_duration_total": 0.02043212998250965, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312686.123323, "idle_time": 0.6622694739999133, "inference_duration_total": 0.022526736996951513, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312686.1239872, "idle_time": 0.6626478379985201, "inference_duration_total": 0.022144197020679712, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312686.7598174, "idle_time": 1.298764057995868, "inference_duration_total": 0.017341738006507512, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312686.7602234, "idle_time": 1.298884096002439, "inference_duration_total": 0.017733061984472442, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312687.761878, "idle_time": 2.300824730999011, "inference_duration_total": 0.01775929999712389, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312687.7623155, "idle_time": 2.3009759830019902, "inference_duration_total": 0.018080004992953036, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312688.8114326, "idle_time": 3.350093253000523, "inference_duration_total": 0.023017561979941092, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312688.8116193, "idle_time": 3.3505660599985276, "inference_duration_total": 0.024305208018631674, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312689.810578, "idle_time": 4.349524771998404, "inference_duration_total": 0.021763505996204913, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312689.8110707, "idle_time": 4.349731312002405, "inference_duration_total": 0.021999784978106618, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312690.8598359, "idle_time": 5.398782621996361, "inference_duration_total": 0.017198238012497313, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312690.8603735, "idle_time": 5.399033909998252, "inference_duration_total": 0.01789893498062156, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312691.8598423, "idle_time": 6.398789054001099, "inference_duration_total": 0.017789505975088105, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312691.860267, "idle_time": 6.398927663001814, "inference_duration_total": 0.018312263986445032, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312692.9094143, "idle_time": 7.448361190996366, "inference_duration_total": 0.01880716599407606, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312692.9099753, "idle_time": 7.448635708999063, "inference_duration_total": 0.02060618399264058, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312693.915274, "idle_time": 8.454220578998502, "inference_duration_total": 0.01966210602404317, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312693.9157581, "idle_time": 8.454418497000006, "inference_duration_total": 0.019823940987407696, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312694.959952, "idle_time": 9.498898870995617, "inference_duration_total": 0.018158103011955973, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312694.9605794, "idle_time": 9.499239826000121, "inference_duration_total": 0.01915737599483691, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312695.9599495, "idle_time": 10.49889616699511, "inference_duration_total": 0.020205582019116264, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312695.9603646, "idle_time": 10.499024978002126, "inference_duration_total": 0.019825749026495032, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312697.0095766, "idle_time": 11.548523060999287, "inference_duration_total": 0.0163041789928684, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312697.0101514, "idle_time": 11.548811874999956, "inference_duration_total": 0.017171186009363737, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312698.0096974, "idle_time": 12.548644264999893, "inference_duration_total": 0.0175860370072769, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312698.0101192, "idle_time": 12.548779723001644, "inference_duration_total": 0.017612103001738433, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312699.0608838, "idle_time": 13.599544101001811, "inference_duration_total": 0.01909828497446142, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312699.0610552, "idle_time": 13.60000202000083, "inference_duration_total": 0.01915923803608166, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312700.06009, "idle_time": 14.598750721001124, "inference_duration_total": 0.0209641799738165, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754312700.0603082, "idle_time": 14.599254838998604, "inference_duration_total": 0.02022357100941008, "inference_count": 32, "speech_id": null, "error": null}], "eou": []}}, "assistant_turn": {"timestamp": "2025-08-04T13:05:00.885986", "content": "Manual lending ek aisa process hai jahan aap khud har borrower ko choose karte hain. Aap sirf two hundred fifty rupees se shuru kar sakte hain aur borrowers ke profile, income, aur", "function_calls": [{"timestamp": "2025-08-04T13:04:50.463260", "function_name": "search_knowledge_base", "arguments": {"query": "manual lending in P2P"}, "result": "1. 📌 Question: What is manual lending?\n💡 Answer: In Manual lending - you pick each individual borrower yourself. You can start with just 250 rupees per loan per borrower and choose based on their prof..."}], "metrics": {"llm": [{"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C0p9siZ9wj0VvqR7AZ1IbnXZI3KWy", "timestamp": 1754312689.4180768, "duration": 1.1594508529960876, "ttft": 1.1456099459974212, "cancelled": false, "completion_tokens": 21, "prompt_tokens": 1080, "total_tokens": 1101, "tokens_per_second": 18.112022554241772}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C0p9uVsWstCNNgM1KfN5vVxiz5hmm", "timestamp": 1754312692.8930137, "duration": 2.428692891000537, "ttft": 0.6650740909972228, "cancelled": false, "completion_tokens": 96, "prompt_tokens": 1214, "total_tokens": 1310, "tokens_per_second": 39.5274348418961}], "tts": [{"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "51f1052b6e3b", "timestamp": 1754312694.749872, "ttfb": 0.2994213359997957, "duration": 3.1721115529944655, "audio_duration": 20.96733333333329, "cancelled": false, "characters_count": 375, "streamed": true, "speech_id": "speech_41e0757b0e5a", "error": null}]}}}], "usage_summary": "UsageSummary(llm_prompt_tokens=5998, llm_prompt_cached_tokens=2048, llm_completion_tokens=272, tts_characters_count=802, tts_audio_duration=45.5893333333333, stt_audio_duration=7.600000000000001)"}