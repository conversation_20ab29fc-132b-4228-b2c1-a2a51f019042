{"session_info": {"session_name": "playground-kDLh-CHQU", "start_time": "2025-08-02T06:44:28.340103", "end_time": "2025-08-02T06:46:00.402941", "duration_seconds": 92.062838}, "llm_metrics": [{"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C00GjLsqIJrduVZwVkCEnNacBzBMO", "timestamp": 1754117070.293247, "duration": 1.804480267001054, "ttft": 1.2702310680033406, "cancelled": false, "completion_tokens": 29, "prompt_tokens": 1071, "total_tokens": 1100, "tokens_per_second": 16.071109521300773}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C00GrfRliaU3gTjBtKMsxTMcHzeuK", "timestamp": 1754117078.2370167, "duration": 1.0137420129976817, "ttft": 0.6224547389974759, "cancelled": false, "completion_tokens": 23, "prompt_tokens": 1064, "total_tokens": 1087, "tokens_per_second": 22.688218210457652}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C00H0X0dDd2bfOjMhh4wSR3PRcFTP", "timestamp": 1754117087.4097502, "duration": 1.214297381000506, "ttft": 0.6523996010000701, "cancelled": false, "completion_tokens": 43, "prompt_tokens": 1097, "total_tokens": 1140, "tokens_per_second": 35.41142447706727}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C00H5Uemm0s4mPviquGDJAZbK0Vd8", "timestamp": 1754117093.2264028, "duration": 1.6179554560003453, "ttft": 0.6776300239980628, "cancelled": false, "completion_tokens": 64, "prompt_tokens": 1105, "total_tokens": 1169, "tokens_per_second": 39.55609517100719}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C00HTdCVuB7GIxKs2dTCovusfOK2G", "timestamp": 1754117117.0450857, "duration": 1.7830348050010798, "ttft": 0.6246528580013546, "cancelled": false, "completion_tokens": 64, "prompt_tokens": 1208, "total_tokens": 1272, "tokens_per_second": 35.89385906572993}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C00HYOLDKDWYPizcoRtJ59BBlK6Oj", "timestamp": 1754117121.7119505, "duration": 1.5466682569967816, "ttft": 1.4311664199994993, "cancelled": false, "completion_tokens": 52, "prompt_tokens": 1217, "total_tokens": 1269, "tokens_per_second": 33.62065508538345}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C00HboZcLa3WRYf2MqT3yhEsA0zXB", "timestamp": 1754117125.2740114, "duration": 2.4943495170009555, "ttft": 0.6393731899988779, "cancelled": false, "completion_tokens": 110, "prompt_tokens": 1590, "total_tokens": 1700, "tokens_per_second": 44.099673782789225}], "stt_metrics": [], "tts_metrics": [{"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "2b2c5e87b402", "timestamp": 1754117071.3728116, "ttfb": 0.23385346600116463, "duration": 1.081010136000259, "audio_duration": 6.927333333333337, "cancelled": false, "characters_count": 110, "streamed": true, "speech_id": "speech_f23cd6b92596", "error": null}, {"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "b84853542883", "timestamp": 1754117078.8826113, "ttfb": 0.27474748299937346, "duration": 0.9078217419992143, "audio_duration": 5.247333333333335, "cancelled": false, "characters_count": 92, "streamed": true, "speech_id": "speech_a38737863dc3", "error": null}, {"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "271d81276a37", "timestamp": 1754117088.4787774, "ttfb": 0.2664377090004564, "duration": 1.4059493280001334, "audio_duration": 9.62733333333333, "cancelled": false, "characters_count": 175, "streamed": true, "speech_id": "speech_7e43824c8c8c", "error": null}, {"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "02b5c2426f37", "timestamp": 1754117094.3238695, "ttfb": 0.2567118280021532, "duration": 1.815522114000487, "audio_duration": 13.16733333333332, "cancelled": false, "characters_count": 248, "streamed": true, "speech_id": "speech_d06de98c918a", "error": null}, {"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "3deed73253ca", "timestamp": 1754117118.9402494, "ttfb": 0.30321337100031087, "duration": 2.6497107240029436, "audio_duration": 19.347333333333296, "cancelled": false, "characters_count": 364, "streamed": true, "speech_id": "speech_eadaa0ecc454", "error": null}, {"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "777768993dbb", "timestamp": 1754117127.7293117, "ttfb": 0.26290262100155815, "duration": 4.15941494100116, "audio_duration": 31.347333333333253, "cancelled": false, "characters_count": 576, "streamed": true, "speech_id": "speech_43efc858c54e", "error": null}], "eou_metrics": [], "vad_metrics": [{"type": "vad_metrics", "timestamp": 1754117071.0965188, "idle_time": 0.09632825199878425, "inference_duration_total": 0.027203652996831806, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117071.097496, "idle_time": 0.09731757999907131, "inference_duration_total": 0.027517697999428492, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117072.0952458, "idle_time": 0.4508200089985621, "inference_duration_total": 0.023325207988818875, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117072.0957336, "idle_time": 0.4511107289981737, "inference_duration_total": 0.02260039100656286, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117073.1443412, "idle_time": 1.4999154700017243, "inference_duration_total": 0.02102825199472136, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117073.1448777, "idle_time": 1.5002543890004745, "inference_duration_total": 0.021261875004711328, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117074.1464112, "idle_time": 2.5019853859994328, "inference_duration_total": 0.020546199986711144, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117074.146869, "idle_time": 2.502245892999781, "inference_duration_total": 0.020376563996251207, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117075.1945343, "idle_time": 3.5501087340016966, "inference_duration_total": 0.01808251400871086, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117075.1949654, "idle_time": 3.5503420579989324, "inference_duration_total": 0.018122625006071758, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117076.1946824, "idle_time": 4.55025637899962, "inference_duration_total": 0.016711185016902164, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117076.1951132, "idle_time": 4.550490129997343, "inference_duration_total": 0.017010971998388413, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117077.2447553, "idle_time": 5.600329251999938, "inference_duration_total": 0.01652859599562362, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117077.2448854, "idle_time": 5.6002624569991895, "inference_duration_total": 0.016146657999343006, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117078.2462049, "idle_time": 6.6015817019979295, "inference_duration_total": 0.018541376000939636, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117078.2464645, "idle_time": 6.602038697001262, "inference_duration_total": 0.019705781000084244, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117079.2946856, "idle_time": 7.650259805999667, "inference_duration_total": 0.019169786010024836, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117079.2954652, "idle_time": 7.650842014998489, "inference_duration_total": 0.018592319003801094, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117080.2959833, "idle_time": 8.651557571000012, "inference_duration_total": 0.016666852003254462, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117080.2966182, "idle_time": 8.651995110998541, "inference_duration_total": 0.01713796500916942, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117081.3442445, "idle_time": 9.699818730001425, "inference_duration_total": 0.01836467000612174, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117081.3448153, "idle_time": 9.70019221399707, "inference_duration_total": 0.017801082001824398, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117082.344483, "idle_time": 10.70005702400158, "inference_duration_total": 0.01584536598966224, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117082.3449316, "idle_time": 10.700308390998543, "inference_duration_total": 0.016769226986070862, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117083.3944807, "idle_time": 11.750054756001191, "inference_duration_total": 0.018292344015208073, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117083.3950942, "idle_time": 11.750471096998808, "inference_duration_total": 0.019055832006415585, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117084.3944097, "idle_time": 12.749983877001796, "inference_duration_total": 0.016847596012667054, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117084.3948452, "idle_time": 12.750221905000217, "inference_duration_total": 0.016793978000350762, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117085.4446778, "idle_time": 13.800252185999852, "inference_duration_total": 0.017082679016311886, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117085.4454901, "idle_time": 13.80086703899724, "inference_duration_total": 0.01763584000946139, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117086.4446204, "idle_time": 14.800194354000269, "inference_duration_total": 0.017988177994993748, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117086.4450734, "idle_time": 14.800450232000003, "inference_duration_total": 0.017774537998775486, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117087.4945142, "idle_time": 15.850088481998682, "inference_duration_total": 0.0175981090105779, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117087.495121, "idle_time": 15.850497727999027, "inference_duration_total": 0.01933662000010372, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117088.4950237, "idle_time": 16.850597788001323, "inference_duration_total": 0.019749318991671316, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117088.4954276, "idle_time": 16.8508044659975, "inference_duration_total": 0.01900981500148191, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117089.544714, "idle_time": 17.900090903996897, "inference_duration_total": 0.016519730997970328, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117089.5461721, "idle_time": 17.901746344999992, "inference_duration_total": 0.01764646399897174, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117090.5445051, "idle_time": 18.900079384999117, "inference_duration_total": 0.01848279100158834, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117090.5449548, "idle_time": 18.900331698998343, "inference_duration_total": 0.019492439001624007, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117091.5939336, "idle_time": 19.94950757300103, "inference_duration_total": 0.01624326198725612, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117091.5945413, "idle_time": 19.949918196998624, "inference_duration_total": 0.016975839993392583, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117092.5938957, "idle_time": 20.949469696999586, "inference_duration_total": 0.01866953900025692, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117092.5943134, "idle_time": 20.949690264998935, "inference_duration_total": 0.018325922992517008, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117093.64412, "idle_time": 21.999694243000704, "inference_duration_total": 0.018431986980431248, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117093.6447217, "idle_time": 22.000098557997262, "inference_duration_total": 0.018751472995063523, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117094.6446064, "idle_time": 23.000180685001396, "inference_duration_total": 0.023789224011125043, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117094.645106, "idle_time": 23.000482863997604, "inference_duration_total": 0.023882521007180912, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117095.6942773, "idle_time": 24.0498514969986, "inference_duration_total": 0.017293623997829854, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117095.694876, "idle_time": 24.050252724999154, "inference_duration_total": 0.017002012002194533, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117096.6942751, "idle_time": 25.04984917299953, "inference_duration_total": 0.01702119401670643, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117096.694479, "idle_time": 25.04985581499932, "inference_duration_total": 0.016755426000599982, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117097.6949415, "idle_time": 26.050515482002083, "inference_duration_total": 0.01757604798694956, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117097.6954172, "idle_time": 26.05079396399742, "inference_duration_total": 0.01754561498455587, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117098.744557, "idle_time": 27.100130913000612, "inference_duration_total": 0.016204765990551095, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117098.7449958, "idle_time": 27.10037271299734, "inference_duration_total": 0.016544826994504547, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117099.7450457, "idle_time": 28.10042249399703, "inference_duration_total": 0.017852195000159554, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117099.745275, "idle_time": 28.10084933300095, "inference_duration_total": 0.017379300988977775, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117100.7949562, "idle_time": 29.150333161000162, "inference_duration_total": 0.01754486500067287, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117100.795129, "idle_time": 29.15070333100084, "inference_duration_total": 0.0176926599961007, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117101.7952268, "idle_time": 30.150603791997128, "inference_duration_total": 0.01743373801582493, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117101.7954178, "idle_time": 30.150992003000283, "inference_duration_total": 0.01742047199877561, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117102.8439777, "idle_time": 31.199551965000865, "inference_duration_total": 0.016359987002942944, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117102.8441372, "idle_time": 31.199514079999062, "inference_duration_total": 0.016371473990147933, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117103.8455138, "idle_time": 32.201087896999525, "inference_duration_total": 0.017323801996099064, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117103.8459706, "idle_time": 32.201347369998985, "inference_duration_total": 0.01780649900683784, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117104.8938305, "idle_time": 33.2494047379987, "inference_duration_total": 0.01630705401476007, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117104.8943808, "idle_time": 33.24975760400048, "inference_duration_total": 0.01681071201164741, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117105.8948333, "idle_time": 34.25040753800204, "inference_duration_total": 0.01675074900413165, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117105.895249, "idle_time": 34.25062564499967, "inference_duration_total": 0.017008580998663092, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117106.9447362, "idle_time": 35.30031033799969, "inference_duration_total": 0.01788057700105128, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117106.9453118, "idle_time": 35.300688644998445, "inference_duration_total": 0.017835949005529983, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117107.9448285, "idle_time": 36.30040254800042, "inference_duration_total": 0.017305831988778664, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117107.9452722, "idle_time": 36.3006490709995, "inference_duration_total": 0.0165045320027275, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117108.9943283, "idle_time": 37.34990244900109, "inference_duration_total": 0.01588556899514515, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117108.9949389, "idle_time": 37.350315787, "inference_duration_total": 0.016444098007923458, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117109.994787, "idle_time": 38.35016398299922, "inference_duration_total": 0.01709171798574971, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117109.9949665, "idle_time": 38.35054076600136, "inference_duration_total": 0.0175254740024684, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117111.0444956, "idle_time": 39.400069789000554, "inference_duration_total": 0.018051909006317146, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117111.0451021, "idle_time": 39.4004788309976, "inference_duration_total": 0.017897713994898368, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117112.0451324, "idle_time": 40.400706462001835, "inference_duration_total": 0.01694107399089262, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117112.0456057, "idle_time": 40.400982357998146, "inference_duration_total": 0.017647305994614726, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117113.0943952, "idle_time": 41.449969247998524, "inference_duration_total": 0.018808107994118473, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117113.095021, "idle_time": 41.450397778997285, "inference_duration_total": 0.019004134002898354, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117114.0945718, "idle_time": 42.450145966999116, "inference_duration_total": 0.017629325007874286, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117114.095048, "idle_time": 42.450424835999, "inference_duration_total": 0.017732943000737578, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117115.1444824, "idle_time": 43.50005648899969, "inference_duration_total": 0.018660338995687198, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117115.1448274, "idle_time": 43.50020434099861, "inference_duration_total": 0.01964347600733163, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117116.144136, "idle_time": 44.49971005900079, "inference_duration_total": 0.01889034200939932, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117116.144568, "idle_time": 44.499944780996884, "inference_duration_total": 0.019394299994019093, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117117.196695, "idle_time": 45.55226911999853, "inference_duration_total": 0.018249665008625016, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117117.1974468, "idle_time": 45.55282373099908, "inference_duration_total": 0.018670303008548217, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117118.19445, "idle_time": 46.55002403300023, "inference_duration_total": 0.020475864006584743, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117118.1951375, "idle_time": 46.55051433299741, "inference_duration_total": 0.02138030800051638, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117119.2445903, "idle_time": 47.60016444800203, "inference_duration_total": 0.025318993994005723, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117119.2447512, "idle_time": 47.600128043999575, "inference_duration_total": 0.02704964900840423, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117120.2441204, "idle_time": 48.59969438299959, "inference_duration_total": 0.01666460901105893, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117120.2445436, "idle_time": 48.59992029099885, "inference_duration_total": 0.01668083999902592, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117121.2944493, "idle_time": 49.65002341099898, "inference_duration_total": 0.017413356999895768, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117121.2949977, "idle_time": 49.65037458000006, "inference_duration_total": 0.01806031700107269, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117122.2952075, "idle_time": 50.650584104998416, "inference_duration_total": 0.016671984983986476, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117122.2954204, "idle_time": 50.65099441100028, "inference_duration_total": 0.017279771989706205, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117123.2949328, "idle_time": 51.65050685800088, "inference_duration_total": 0.019406739993428346, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117123.295369, "idle_time": 51.65074589899814, "inference_duration_total": 0.018554961010522675, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117124.3453395, "idle_time": 52.70091392500035, "inference_duration_total": 0.017125593003584072, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117124.3457758, "idle_time": 52.70115272199837, "inference_duration_total": 0.01767837798615801, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117125.3514931, "idle_time": 53.70706722799878, "inference_duration_total": 0.023594964000949403, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117125.352125, "idle_time": 53.70750164799756, "inference_duration_total": 0.024402851988270413, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117126.3945441, "idle_time": 54.749921063998045, "inference_duration_total": 0.017130912994616665, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117126.399296, "idle_time": 54.75487031100056, "inference_duration_total": 0.02237068700924283, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117127.401291, "idle_time": 55.75666789999741, "inference_duration_total": 0.02383034698868869, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117127.4015138, "idle_time": 55.7570878660008, "inference_duration_total": 0.024062482003500918, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117128.444598, "idle_time": 56.80017219900037, "inference_duration_total": 0.017903429001307813, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117128.4447699, "idle_time": 56.80014659599692, "inference_duration_total": 0.017693254008918302, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117129.445115, "idle_time": 57.800689294999756, "inference_duration_total": 0.018146129998058314, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117129.4455721, "idle_time": 57.80094887700034, "inference_duration_total": 0.018388100990705425, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117130.4941006, "idle_time": 58.849674655000854, "inference_duration_total": 0.019682996997289592, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117130.4948008, "idle_time": 58.850177731997974, "inference_duration_total": 0.01886989100239589, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117131.4949558, "idle_time": 59.85052992599958, "inference_duration_total": 0.01803551500051981, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117131.4954073, "idle_time": 59.85078410499773, "inference_duration_total": 0.0171109519978927, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117132.5440545, "idle_time": 60.89962857900173, "inference_duration_total": 0.019556961000489537, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117132.5446103, "idle_time": 60.8999872280001, "inference_duration_total": 0.018668684988369932, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117133.5444887, "idle_time": 61.900062769000215, "inference_duration_total": 0.016215204996115062, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117133.5447645, "idle_time": 61.900141201000224, "inference_duration_total": 0.016338028999598464, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117134.594829, "idle_time": 62.950403246999485, "inference_duration_total": 0.01777655100886477, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754117134.595137, "idle_time": 62.95051366199914, "inference_duration_total": 0.017744715987646487, "inference_count": 32, "speech_id": null, "error": null}], "conversation_history": [{"timestamp": "2025-08-02T06:44:28.487173", "role": "system", "content": "Greet the user warmly and ask them how you can help them today about p2p lending."}, {"timestamp": "2025-08-02T06:44:31.446634", "role": "assistant", "content": "Hello! I'm"}, {"timestamp": "2025-08-02T06:44:37.229721", "role": "user", "content": "in a"}, {"timestamp": "2025-08-02T06:44:43.498389", "role": "assistant", "content": "It seems like your message got cut off. Can you please share a bit more? I'm here to listen!"}, {"timestamp": "2025-08-02T06:44:46.201777", "role": "user", "content": "who are u"}, {"timestamp": "2025-08-02T06:44:51.614706", "role": "user", "content": "who is bhavin"}, {"timestamp": "2025-08-02T06:44:51.615597", "role": "assistant", "content": "I'm <PERSON><PERSON><PERSON>, a P to P lending advocate at लेन-देन Club. I have ten years of experience in"}, {"timestamp": "2025-08-02T06:45:05.933167", "role": "assistant", "content": "I'm <PERSON><PERSON><PERSON>, a P to P lending advocate at लेन-देन Club. <PERSON><PERSON><PERSON> is one of the founders of लेन-देन Club, and he has played a significant role in shaping our platform for P to P lending. Would you like to know more about P to P lending or our mission?"}, {"timestamp": "2025-08-02T06:45:15.268584", "role": "user", "content": "what is manual lending"}, {"timestamp": "2025-08-02T06:45:20.172439", "role": "user", "content": "what is lumpsum lending"}, {"timestamp": "2025-08-02T06:45:20.173299", "role": "assistant", "content": "Manual lending refers to the traditional process of lending where individuals"}, {"timestamp": "2025-08-02T06:45:35.452274", "role": "assistant", "content": "Manual lending is when you choose each individual borrower yourself. You can start with just two hundred fifty rupees per loan and select borrowers based on their profile and income, giving you complete control.\n\nOn the other"}], "function_calls": [{"timestamp": "2025-08-02T06:45:22.776953", "function_name": "search_knowledge_base", "arguments": {"query": "manual lending"}, "result": "1. 📌 Question: What is manual lending?\n💡 Answer: In Manual lending - you pick each individual borrower yourself. You can start with just 250 rupees per loan per borrower and choose based on their prof..."}, {"timestamp": "2025-08-02T06:45:22.778523", "function_name": "search_knowledge_base", "arguments": {"query": "lumpsum lending"}, "result": "1. 📌 Question: What is Lumpsum lending?\n💡 Answer: Lumpsum is a lending option that allows you to lend large amounts, starting from ₹25,000 up to ₹25,00,000 under one scheme in one go.\n\n2. 📌 Question: ..."}], "usage_summary": "UsageSummary(llm_prompt_tokens=8352, llm_prompt_cached_tokens=4224, llm_completion_tokens=385, tts_characters_count=1565, tts_audio_duration=85.66399999999987, stt_audio_duration=1.204)"}