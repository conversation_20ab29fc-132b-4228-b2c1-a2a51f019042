{"session_info": {"session_name": "playground-rZZu-4Bqq", "start_time": "2025-08-01T16:05:38.817590", "end_time": "2025-08-01T16:07:25.000349", "duration_seconds": 106.182759}, "llm_metrics": [{"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-BzmYFj1KGn27R8lQNbQdMmqMlnzJG", "timestamp": 1754064341.0648203, "duration": 2.06524249100039, "ttft": 1.3843809429999965, "cancelled": false, "completion_tokens": 29, "prompt_tokens": 968, "total_tokens": 997, "tokens_per_second": 14.041934603985698}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-BzmYNdYZwwDjdKNt2QI6bU4sEA3uF", "timestamp": 1754064348.9635026, "duration": 1.8621868240006734, "ttft": 1.830799539002328, "cancelled": false, "completion_tokens": 23, "prompt_tokens": 958, "total_tokens": 981, "tokens_per_second": 12.351070098642092}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-BzmYR6lRr52Nt29bA5lmjblFCR8aO", "timestamp": 1754064352.7141776, "duration": 1.8987002149988257, "ttft": 1.173626397998305, "cancelled": false, "completion_tokens": 45, "prompt_tokens": 1065, "total_tokens": 1110, "tokens_per_second": 23.700423923967286}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-BzmYaPUptS4flo9wbXvVgAqWXra90", "timestamp": 1754064361.5701723, "duration": 1.2715875249996316, "ttft": 1.2707516600021336, "cancelled": false, "completion_tokens": 23, "prompt_tokens": 996, "total_tokens": 1019, "tokens_per_second": 18.087626331507668}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-BzmYfSHuKzQ6hnm5hOCqK1MuGWW0b", "timestamp": 1754064367.9857838, "duration": 3.0161517950000416, "ttft": 1.1813871880003717, "cancelled": false, "completion_tokens": 113, "prompt_tokens": 1103, "total_tokens": 1216, "tokens_per_second": 37.46495789347314}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-BzmZ6drN6ypxuUdD8NXshWAwyBvRR", "timestamp": 1754064394.5105813, "duration": 2.8903900209988933, "ttft": 2.875911798000743, "cancelled": false, "completion_tokens": 21, "prompt_tokens": 1154, "total_tokens": 1175, "tokens_per_second": 7.265455473978762}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-BzmZAcLCnsaJr2Sh92nvA4L5Hm7Tn", "timestamp": 1754064397.8861635, "duration": 1.7899451039993437, "ttft": 0.6429192800023884, "cancelled": false, "completion_tokens": 71, "prompt_tokens": 1301, "total_tokens": 1372, "tokens_per_second": 39.66602095302361}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-BzmZRyrKtnty2oW83bzDHGQPtQqeT", "timestamp": 1754064413.9607847, "duration": 1.1392577689985046, "ttft": 1.1353870679995453, "cancelled": false, "completion_tokens": 20, "prompt_tokens": 1382, "total_tokens": 1402, "tokens_per_second": 17.555289543982255}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-BzmZTCJZSydj6ljioJyyDz2y4t4OP", "timestamp": 1754064417.9283988, "duration": 2.4954565029984224, "ttft": 0.8319631189988286, "cancelled": false, "completion_tokens": 96, "prompt_tokens": 1537, "total_tokens": 1633, "tokens_per_second": 38.46991517770434}], "stt_metrics": [], "tts_metrics": [{"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "72d39de844f3", "timestamp": 1754064341.9417067, "ttfb": 0.30501598300179467, "duration": 1.0724939120009367, "audio_duration": 6.687333333333337, "cancelled": false, "characters_count": 110, "streamed": true, "speech_id": "speech_1c926adfc5b9", "error": null}, {"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "f0cecb31ea58", "timestamp": 1754064354.0010629, "ttfb": 0.564093140001205, "duration": 1.4663627500012808, "audio_duration": 8.667333333333335, "cancelled": false, "characters_count": 147, "streamed": true, "speech_id": "speech_65d8a3b806bd", "error": null}, {"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "38d8199e397b", "timestamp": 1754064370.4451802, "ttfb": 0.7581209849995503, "duration": 3.8331447439995827, "audio_duration": 24.32733333333328, "cancelled": false, "characters_count": 442, "streamed": true, "speech_id": "speech_3d9d942221db", "error": null}, {"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "d388035b90fd", "timestamp": 1754064399.5921876, "ttfb": 0.33013408400074695, "duration": 2.2570961290002742, "audio_duration": 15.927333333333308, "cancelled": false, "characters_count": 308, "streamed": true, "speech_id": "speech_87d9fadf3c45", "error": null}, {"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "d544ac0329de", "timestamp": 1754064419.8953972, "ttfb": 0.30990608200227143, "duration": 2.8749789590001456, "audio_duration": 20.90733333333329, "cancelled": false, "characters_count": 392, "streamed": true, "speech_id": "speech_c800a1de8dd4", "error": null}], "eou_metrics": [], "vad_metrics": [{"type": "vad_metrics", "timestamp": 1754064340.3670068, "idle_time": 1.3701122899983602, "inference_duration_total": 0.027648495997709688, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064340.3674657, "idle_time": 1.368816459998925, "inference_duration_total": 0.026046457005577395, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064341.3679762, "idle_time": 2.3710815720005485, "inference_duration_total": 0.026061956985358847, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064341.3685756, "idle_time": 2.3699260039975343, "inference_duration_total": 0.026131426988285966, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064342.4167287, "idle_time": 3.4198342009985936, "inference_duration_total": 0.027564418996917084, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064342.4173458, "idle_time": 3.418696459997591, "inference_duration_total": 0.026960501010762528, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064343.416592, "idle_time": 4.419697274999635, "inference_duration_total": 0.025566709009581245, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064343.4171703, "idle_time": 4.418520887000341, "inference_duration_total": 0.02546997301033116, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064344.467014, "idle_time": 5.470119441000861, "inference_duration_total": 0.024485685007675784, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064344.4671986, "idle_time": 5.468549280998559, "inference_duration_total": 0.02363525301188929, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064345.4669933, "idle_time": 6.470098785997834, "inference_duration_total": 0.024054908004472964, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064345.467518, "idle_time": 6.468868774998555, "inference_duration_total": 0.025504655994154746, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064346.5163074, "idle_time": 7.519412844998442, "inference_duration_total": 0.0258426340042206, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064346.516968, "idle_time": 7.518318638998608, "inference_duration_total": 0.024974986001325306, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064347.517105, "idle_time": 8.520210511000187, "inference_duration_total": 0.026414703996124445, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064347.5176778, "idle_time": 8.519028314000025, "inference_duration_total": 0.02663881200714968, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064348.5668077, "idle_time": 9.568158513997332, "inference_duration_total": 0.024472355995385442, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064348.56702, "idle_time": 9.570125555997947, "inference_duration_total": 0.02593159000389278, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064349.567439, "idle_time": 10.570544440000958, "inference_duration_total": 0.0273124449995521, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064349.568124, "idle_time": 10.569474736999837, "inference_duration_total": 0.02787812600217876, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064350.6164465, "idle_time": 11.619551956999203, "inference_duration_total": 0.026599575005093357, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064350.61736, "idle_time": 11.618710761998955, "inference_duration_total": 0.028495500006101793, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064351.6168728, "idle_time": 12.618223391997162, "inference_duration_total": 0.024727342006372055, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064351.617139, "idle_time": 12.620244666999497, "inference_duration_total": 0.02830954899036442, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064352.6667118, "idle_time": 13.66981726999802, "inference_duration_total": 0.025048874009371502, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064352.6675775, "idle_time": 13.668928284998401, "inference_duration_total": 0.02423492200250621, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064353.66674, "idle_time": 14.669845308999356, "inference_duration_total": 0.025821416995313484, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064353.6673026, "idle_time": 14.668653189997713, "inference_duration_total": 0.024696535012481036, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064354.716407, "idle_time": 15.719512283998483, "inference_duration_total": 0.027017633990908507, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064354.7171621, "idle_time": 15.718512769999506, "inference_duration_total": 0.030103393997706007, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064355.718126, "idle_time": 16.71947669599831, "inference_duration_total": 0.023121690006519202, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064355.718976, "idle_time": 16.722081264000735, "inference_duration_total": 0.02484766800989746, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064356.7666073, "idle_time": 17.769712745997822, "inference_duration_total": 0.02297490799537627, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064356.7673268, "idle_time": 17.768677336996916, "inference_duration_total": 0.02255637298731017, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064357.7672281, "idle_time": 18.77033341999777, "inference_duration_total": 0.02518696101833484, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064357.767776, "idle_time": 18.76912648099824, "inference_duration_total": 0.022749481002392713, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064358.816474, "idle_time": 19.819579243998305, "inference_duration_total": 0.025161507997836452, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064358.8166578, "idle_time": 19.81800828300038, "inference_duration_total": 0.024793307009531418, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064359.8159277, "idle_time": 20.819033329000376, "inference_duration_total": 0.02347317000385374, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064359.8164759, "idle_time": 20.81782650199966, "inference_duration_total": 0.023295378014154267, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064360.8666167, "idle_time": 21.869722277999244, "inference_duration_total": 0.024858041007973952, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064360.8680294, "idle_time": 21.86938008299694, "inference_duration_total": 0.02394916000048397, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064361.866865, "idle_time": 22.869970141000522, "inference_duration_total": 0.026165013001445914, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064361.8670611, "idle_time": 22.868411880997883, "inference_duration_total": 0.02410160699582775, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064362.916595, "idle_time": 23.919700179998472, "inference_duration_total": 0.0234523530016304, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064362.9174864, "idle_time": 23.91883696599689, "inference_duration_total": 0.02270460100407945, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064363.9167774, "idle_time": 24.919882689999213, "inference_duration_total": 0.024500545994669665, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064363.9169872, "idle_time": 24.91833766399941, "inference_duration_total": 0.022765768004319398, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064364.9667532, "idle_time": 25.968103795999923, "inference_duration_total": 0.022234386004129192, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064364.9669652, "idle_time": 25.97007051699984, "inference_duration_total": 0.023572566999064293, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064365.9667735, "idle_time": 26.968124170998635, "inference_duration_total": 0.025351554992084857, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064365.9669847, "idle_time": 26.97009008799796, "inference_duration_total": 0.02748193099614582, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064366.9673645, "idle_time": 27.970470064999972, "inference_duration_total": 0.025016162002430065, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064366.9677494, "idle_time": 27.969099911999365, "inference_duration_total": 0.02297403301417944, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064368.0162385, "idle_time": 29.017589012997632, "inference_duration_total": 0.025143602993921377, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064368.0164626, "idle_time": 29.01956802999848, "inference_duration_total": 0.025594305006961804, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064369.0168571, "idle_time": 30.01996236999912, "inference_duration_total": 0.035231453981396044, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064369.0174358, "idle_time": 30.01878649599894, "inference_duration_total": 0.028747585991368396, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064370.0666778, "idle_time": 31.06978305499797, "inference_duration_total": 0.02589908199297497, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064370.06686, "idle_time": 31.06821059899812, "inference_duration_total": 0.02394348199959495, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064371.067212, "idle_time": 32.07031745799759, "inference_duration_total": 0.03409018799357, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064371.06779, "idle_time": 32.06914074499946, "inference_duration_total": 0.03134519200466457, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064372.1164446, "idle_time": 33.11954999599766, "inference_duration_total": 0.024387395998928696, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064372.1166236, "idle_time": 33.11797421499796, "inference_duration_total": 0.022545824009284843, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064373.1176884, "idle_time": 34.12079382799857, "inference_duration_total": 0.024216398003773065, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064373.118296, "idle_time": 34.11964642899693, "inference_duration_total": 0.023572854981466662, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064374.1665618, "idle_time": 35.169667211997876, "inference_duration_total": 0.02593775399509468, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064374.1674109, "idle_time": 35.168761504999566, "inference_duration_total": 0.02518461600266164, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064375.167057, "idle_time": 36.170162420999986, "inference_duration_total": 0.02440937799110543, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064375.1676981, "idle_time": 36.169048777999706, "inference_duration_total": 0.024476816000969848, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064376.2165139, "idle_time": 37.219619131999934, "inference_duration_total": 0.02640345100007835, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064376.2166998, "idle_time": 37.21805061899795, "inference_duration_total": 0.024046010999882128, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064377.21755, "idle_time": 38.21890078999786, "inference_duration_total": 0.023359324019111227, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064377.217899, "idle_time": 38.22100433599917, "inference_duration_total": 0.025551725004334003, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064378.2666142, "idle_time": 39.26796496299721, "inference_duration_total": 0.022529494985064957, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064378.266807, "idle_time": 39.26991239199924, "inference_duration_total": 0.02449156100919936, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064379.2675984, "idle_time": 40.27070386199921, "inference_duration_total": 0.02485317200262216, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064379.2683837, "idle_time": 40.26973435099717, "inference_duration_total": 0.02483343598578358, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064380.3168294, "idle_time": 41.318180149999534, "inference_duration_total": 0.02471727700321935, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064380.3170447, "idle_time": 41.3201501859985, "inference_duration_total": 0.02572901499661384, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064381.3171942, "idle_time": 42.31854499199835, "inference_duration_total": 0.023808792993804673, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064381.3174336, "idle_time": 42.32053911099865, "inference_duration_total": 0.02742152300925227, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064382.3662286, "idle_time": 43.36933386599776, "inference_duration_total": 0.02635883899347391, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064382.3670325, "idle_time": 43.36838319599701, "inference_duration_total": 0.023499189999711234, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064383.3671305, "idle_time": 44.370235947000765, "inference_duration_total": 0.02897544898223714, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064383.3673155, "idle_time": 44.36866604599709, "inference_duration_total": 0.026693921998230508, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064384.416306, "idle_time": 45.41941130899795, "inference_duration_total": 0.025108255005761748, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064384.4176257, "idle_time": 45.418976344997645, "inference_duration_total": 0.025445503004448256, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064385.4164658, "idle_time": 46.419571000998985, "inference_duration_total": 0.026271064001775812, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064385.4170933, "idle_time": 46.418444117000035, "inference_duration_total": 0.02413237501241383, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064386.466401, "idle_time": 47.469506301000365, "inference_duration_total": 0.026297472013538936, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064386.467231, "idle_time": 47.468581569999515, "inference_duration_total": 0.02414044099714374, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064387.466748, "idle_time": 48.469853312999476, "inference_duration_total": 0.026022420006484026, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064387.46766, "idle_time": 48.46901080799944, "inference_duration_total": 0.024738742995396024, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064388.51745, "idle_time": 49.52055540699803, "inference_duration_total": 0.026478480005607707, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064388.5183153, "idle_time": 49.51966594699843, "inference_duration_total": 0.026869704990531318, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064389.516208, "idle_time": 50.51931311699809, "inference_duration_total": 0.02886107399535831, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064389.5163903, "idle_time": 50.517741136998666, "inference_duration_total": 0.024689863006642554, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064390.566568, "idle_time": 51.56967321999764, "inference_duration_total": 0.0264236240000173, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064390.5673664, "idle_time": 51.568717071997526, "inference_duration_total": 0.02496361199155217, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064391.5664635, "idle_time": 52.56956890999936, "inference_duration_total": 0.02685601299526752, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064391.5670712, "idle_time": 52.568421916999796, "inference_duration_total": 0.02481669600456371, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064392.5668337, "idle_time": 53.56993905999843, "inference_duration_total": 0.026309460001357365, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064392.5674253, "idle_time": 53.56877574999817, "inference_duration_total": 0.023820047987101134, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064393.6165009, "idle_time": 54.61785128399788, "inference_duration_total": 0.022977081003773492, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064393.6166997, "idle_time": 54.61980496400065, "inference_duration_total": 0.02416349798659212, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064394.616954, "idle_time": 55.620059775999835, "inference_duration_total": 0.025597844996809727, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064394.6176245, "idle_time": 55.61897524199958, "inference_duration_total": 0.024234915003034985, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064395.666361, "idle_time": 56.667711660997156, "inference_duration_total": 0.02843544700590428, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064395.6665592, "idle_time": 56.66966450399923, "inference_duration_total": 0.0279908369993791, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064396.6692111, "idle_time": 57.672316478998255, "inference_duration_total": 0.027631614997517318, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064396.669423, "idle_time": 57.67077373099892, "inference_duration_total": 0.02651833100026124, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064397.7159927, "idle_time": 58.71909798499837, "inference_duration_total": 0.0267366120024235, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064397.7165704, "idle_time": 58.71792100899984, "inference_duration_total": 0.02512176199888927, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064398.716985, "idle_time": 59.71833580499879, "inference_duration_total": 0.030208589996618684, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064398.7176, "idle_time": 59.72070548600095, "inference_duration_total": 0.03466899500199361, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064399.7670813, "idle_time": 60.7701866919997, "inference_duration_total": 0.026524164986767573, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064399.7672591, "idle_time": 60.76860981199934, "inference_duration_total": 0.03311180199671071, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064400.767215, "idle_time": 61.770320433999586, "inference_duration_total": 0.025441171015700093, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064400.7678185, "idle_time": 61.76916902799712, "inference_duration_total": 0.023657593999814708, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064401.8165262, "idle_time": 62.81963134699981, "inference_duration_total": 0.025716330001159804, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064401.8172417, "idle_time": 62.818592225998145, "inference_duration_total": 0.025333603996841703, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064402.816845, "idle_time": 63.81995038400055, "inference_duration_total": 0.024617206010589143, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064402.8174078, "idle_time": 63.818758291999984, "inference_duration_total": 0.022915010998985963, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064403.8665123, "idle_time": 64.86961797799813, "inference_duration_total": 0.0238802509920788, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064403.8674073, "idle_time": 64.86875807799879, "inference_duration_total": 0.02343242799543077, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064404.8679886, "idle_time": 65.87109381199843, "inference_duration_total": 0.029211119002866326, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064404.8687246, "idle_time": 65.87007495499711, "inference_duration_total": 0.02889587700701668, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064405.9164677, "idle_time": 66.91957310900034, "inference_duration_total": 0.028681347001111135, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064405.9171941, "idle_time": 66.9185448850003, "inference_duration_total": 0.030151977993227774, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064406.917482, "idle_time": 67.9205874649997, "inference_duration_total": 0.028925113001605496, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064406.9183023, "idle_time": 67.91965293499743, "inference_duration_total": 0.028230705989699345, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064407.9662144, "idle_time": 68.96931962300005, "inference_duration_total": 0.025508408001769567, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064407.9670272, "idle_time": 68.96837781799695, "inference_duration_total": 0.0262272909967578, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064408.9670973, "idle_time": 69.9702026140003, "inference_duration_total": 0.026619732005201513, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064408.9677014, "idle_time": 69.9690521550001, "inference_duration_total": 0.027207636001548963, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064410.0165608, "idle_time": 71.01966622599866, "inference_duration_total": 0.025528981987008592, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064410.0175724, "idle_time": 71.01892285499707, "inference_duration_total": 0.02463820801494876, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064411.0165806, "idle_time": 72.0196858950003, "inference_duration_total": 0.025807271995290648, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064411.017146, "idle_time": 72.01849670799857, "inference_duration_total": 0.025554250005370704, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064412.0667205, "idle_time": 73.06982597800015, "inference_duration_total": 0.023162721998232882, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064412.0673943, "idle_time": 73.06874500999766, "inference_duration_total": 0.022189398005139083, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064413.0664642, "idle_time": 74.06956955299756, "inference_duration_total": 0.026002752001659246, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064413.0666485, "idle_time": 74.06799917799799, "inference_duration_total": 0.023758860002999427, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064414.1164384, "idle_time": 75.11954358299772, "inference_duration_total": 0.026460906981810695, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064414.1174257, "idle_time": 75.11877637699945, "inference_duration_total": 0.024907964987505693, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064415.1172314, "idle_time": 76.11858214200038, "inference_duration_total": 0.023331810993113322, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064415.1174178, "idle_time": 76.12052312000014, "inference_duration_total": 0.02656542598924716, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064416.166941, "idle_time": 77.17004628200084, "inference_duration_total": 0.028039830998750404, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064416.1680808, "idle_time": 77.16943186699791, "inference_duration_total": 0.026753093996376265, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064417.166839, "idle_time": 78.16818945699924, "inference_duration_total": 0.024869386994396336, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064417.1671004, "idle_time": 78.17020576499999, "inference_duration_total": 0.028650591997575248, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064418.1714983, "idle_time": 79.1746036250006, "inference_duration_total": 0.03129357898797025, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064418.172253, "idle_time": 79.17360354399716, "inference_duration_total": 0.02835427800891921, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064419.2166736, "idle_time": 80.21977875099765, "inference_duration_total": 0.03577967398814508, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754064419.2172592, "idle_time": 80.21860987999753, "inference_duration_total": 0.03335956300361431, "inference_count": 32, "speech_id": null, "error": null}], "conversation_history": [{"timestamp": "2025-08-01T16:05:38.997042", "role": "system", "content": "Greet the user warmly and ask them how you can help them today about p2p lending."}, {"timestamp": "2025-08-01T16:05:47.107608", "role": "user", "content": "founders of lenden club"}, {"timestamp": "2025-08-01T16:05:47.108934", "role": "assistant", "content": "Hello! I'm <PERSON><PERSON><PERSON>, and I'm here to help you with anything related to P2P lending. How can I assist you"}, {"timestamp": "2025-08-01T16:06:00.304785", "role": "user", "content": "what is manual lending"}, {"timestamp": "2025-08-01T16:06:00.305935", "role": "assistant", "content": "लेन-देन Club ke founders <PERSON><PERSON><PERSON> aur <PERSON>, aur isse 2015 mein establish kiya gaya tha. <PERSON><PERSON><PERSON> iske"}, {"timestamp": "2025-08-01T16:06:31.627507", "role": "user", "content": "okay, who is <PERSON><PERSON> karki"}, {"timestamp": "2025-08-01T16:06:31.629461", "role": "assistant", "content": "लेन-देन Club ke founders <PERSON><PERSON><PERSON> aur <PERSON>, aur isse 2015 mein establish kiya gaya tha. \n\nAb aap manual lending ke baare mein jaan na chahte hain, right? Manual lending ka matlab hota hai ki lenders aur borrowers ke beech mein direct interaction hota hai, bina kisi automated system ke. Ismein lenders khud borrowers ki profile dekhte hain aur unhe paise dete hain. Aapko iske baare mein aur koi specific information"}, {"timestamp": "2025-08-01T16:06:52.829189", "role": "user", "content": "what is lumpsum lending"}, {"timestamp": "2025-08-01T16:06:52.831924", "role": "assistant", "content": "Dipesh Karki ek experienced tech leader hain, jo pehle IDA Business Solutions aur Newgen Software mein software development lead kar chuke hain. Unka educational background B.Tech in Electronics and Communication Engineering hai, jo unhone NIT Kurukshetra se kiya. Aapko unke baare"}, {"timestamp": "2025-08-01T16:07:00.080459", "role": "assistant", "content": "Lumpsum lending ek aisa option hai jisme aap ek saath ek bada amount"}], "function_calls": [{"timestamp": "2025-08-01T16:05:50.814290", "function_name": "search_knowledge_base", "arguments": {"query": "founders of लेन-देन Club"}, "result": "1. 📌 Question: Who are the founders of LenDenClub?\n💡 Answer: The founders of LenDenClub are <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.\n\n2. 📌 Question: Who founded LenDenClub and in which year?\n💡 Answer: LenDenClu..."}, {"timestamp": "2025-08-01T16:06:04.968550", "function_name": "search_knowledge_base", "arguments": {"query": "founders of लेन-देन Club"}, "result": "1. 📌 Question: Who are the founders of LenDenClub?\n💡 Answer: The founders of LenDenClub are <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.\n\n2. 📌 Question: Who founded LenDenClub and in which year?\n💡 Answer: LenDenClu..."}, {"timestamp": "2025-08-01T16:06:36.095118", "function_name": "search_knowledge_base", "arguments": {"query": "who is <PERSON><PERSON><PERSON>"}, "result": "1. 📌 Question: What prior experience does <PERSON><PERSON><PERSON> have?\n💡 Answer: <PERSON><PERSON><PERSON> previously led software development at IDA Business Solutions and Newgen Software, underpinned by a B.Tech in ECE from NIT..."}, {"timestamp": "2025-08-01T16:06:55.431825", "function_name": "search_knowledge_base", "arguments": {"query": "what is lumpsum lending"}, "result": "1. 📌 Question: What is Lumpsum lending?\n💡 Answer: Lumpsum is a lending option that allows you to lend large amounts, starting from ₹25,000 up to ₹25,00,000 under one scheme in one go.\n\n2. 📌 Question: ..."}], "usage_summary": "UsageSummary(llm_prompt_tokens=10464, llm_prompt_cached_tokens=2176, llm_completion_tokens=441, tts_characters_count=1399, tts_audio_duration=76.51666666666655, stt_audio_duration=0.0)"}