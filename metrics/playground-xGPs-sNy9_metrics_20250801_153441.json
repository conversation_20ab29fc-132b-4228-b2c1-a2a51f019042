{"session_info": {"session_name": "playground-xGPs-sNy9", "start_time": "2025-08-01T15:32:44.401848", "end_time": "2025-08-01T15:34:41.372076", "duration_seconds": 116.970228}, "llm_metrics": [{"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-Bzm2PjLTsmHijtco4JhqGT3uF2Kid", "timestamp": 1754062366.876815, "duration": 2.350891969999793, "ttft": 1.0290740420005022, "cancelled": false, "completion_tokens": 42, "prompt_tokens": 968, "total_tokens": 1010, "tokens_per_second": 17.865559343419637}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-Bzm2YZ9JAaHaiAnCdDb2mv4kTFxYp", "timestamp": 1754062375.0509257, "duration": 1.1035460490002151, "ttft": 1.0712007790007192, "cancelled": false, "completion_tokens": 23, "prompt_tokens": 956, "total_tokens": 979, "tokens_per_second": 20.8419032634274}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-Bzm2bLcJfUkcG2HefcL2ghXUaGd0v", "timestamp": 1754062377.9080074, "duration": 0.9892983719983022, "ttft": 0.5301710049989197, "cancelled": false, "completion_tokens": 33, "prompt_tokens": 1063, "total_tokens": 1096, "tokens_per_second": 33.356973926220746}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-Bzm2kzaM7pKEWknQShxNDPW6lhEK2", "timestamp": 1754062387.1856763, "duration": 0.9098692280003888, "ttft": 0.9058809100006329, "cancelled": false, "completion_tokens": 22, "prompt_tokens": 1152, "total_tokens": 1174, "tokens_per_second": 24.179298873915318}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-Bzm2myGtp6rreytM1uqRUE3ck7iCz", "timestamp": 1754062389.741268, "duration": 1.6686264550007763, "ttft": 0.5366458080006851, "cancelled": false, "completion_tokens": 72, "prompt_tokens": 1287, "total_tokens": 1359, "tokens_per_second": 43.149261947885456}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-Bzm2wXEA9alNzfVJoqDMoTCamR3ZQ", "timestamp": 1754062401.632583, "duration": 3.236921622999944, "ttft": 3.2208203390000563, "cancelled": false, "completion_tokens": 20, "prompt_tokens": 1164, "total_tokens": 1184, "tokens_per_second": 6.178709999615072}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-Bzm31MYa9wRV8FY9jBXZ72F6jwOKl", "timestamp": 1754062405.9446182, "duration": 3.213463754998884, "ttft": 1.4400251969982492, "cancelled": false, "completion_tokens": 55, "prompt_tokens": 1299, "total_tokens": 1354, "tokens_per_second": 17.115487895091913}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-Bzm3Gy2QZtJPbE0On1RuzQ3VRcOhN", "timestamp": 1754062419.064147, "duration": 1.7093546260002768, "ttft": 1.6950451089996932, "cancelled": false, "completion_tokens": 20, "prompt_tokens": 1361, "total_tokens": 1381, "tokens_per_second": 11.700322271217676}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-Bzm3IE11M5IncTdoAyax9iDnaX2ha", "timestamp": 1754062422.0293353, "duration": 1.816192158999911, "ttft": 0.7974627040002815, "cancelled": false, "completion_tokens": 57, "prompt_tokens": 1507, "total_tokens": 1564, "tokens_per_second": 31.384344281822656}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-Bzm3ZHYq0LUyh5dVyq0Ic9FONaBQF", "timestamp": 1754062438.2037132, "duration": 1.1812356560003536, "ttft": 1.1811270069993043, "cancelled": false, "completion_tokens": 23, "prompt_tokens": 1759, "total_tokens": 1782, "tokens_per_second": 19.471135910236285}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-Bzm3bP5Ny4iFyteQwCsc4TjTKyY70", "timestamp": 1754062440.7925665, "duration": 1.6284836029990402, "ttft": 0.5180728939994879, "cancelled": false, "completion_tokens": 77, "prompt_tokens": 1917, "total_tokens": 1994, "tokens_per_second": 47.283251644779}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-Bzm3nBW7adnUFQa0gI9WbqmtVeVpB", "timestamp": 1754062452.794425, "duration": 1.4863632430005964, "ttft": 0.5623046820001036, "cancelled": false, "completion_tokens": 53, "prompt_tokens": 1772, "total_tokens": 1825, "tokens_per_second": 35.65750179142363}], "stt_metrics": [], "tts_metrics": [{"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "02dc1d938b57", "timestamp": 1754062367.6248138, "ttfb": 0.31287784300002386, "duration": 1.1418553029998293, "audio_duration": 7.167333333333337, "cancelled": false, "characters_count": 121, "streamed": true, "speech_id": "speech_4fab4f636771", "error": null}, {"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "a7458924c3d8", "timestamp": 1754062378.945568, "ttfb": 0.6075615500012645, "duration": 1.2624172740015638, "audio_duration": 6.627333333333336, "cancelled": false, "characters_count": 108, "streamed": true, "speech_id": "speech_1dd6a9d358d0", "error": null}, {"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "6a605fc422f6", "timestamp": 1754062390.940162, "ttfb": 0.2918764269998064, "duration": 2.025678104999315, "audio_duration": 13.707333333333317, "cancelled": false, "characters_count": 272, "streamed": true, "speech_id": "speech_6d449d540713", "error": null}, {"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "8fdc2fdd0cd8", "timestamp": 1754062407.433966, "ttfb": 0.36480800499884936, "duration": 2.7698425329999736, "audio_duration": 16.287333333333308, "cancelled": false, "characters_count": 266, "streamed": true, "speech_id": "speech_a25b5e54252a", "error": null}, {"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "13c67f22684c", "timestamp": 1754062423.5747502, "ttfb": 0.3197160760009865, "duration": 1.9846506780013442, "audio_duration": 14.547333333333315, "cancelled": false, "characters_count": 257, "streamed": true, "speech_id": "speech_43048f6697e4", "error": null}, {"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "47218bacf0b3", "timestamp": 1754062442.7248998, "ttfb": 0.3326534059997357, "duration": 2.3893131149998226, "audio_duration": 17.367333333333303, "cancelled": false, "characters_count": 321, "streamed": true, "speech_id": "speech_c71c9ccf1031", "error": null}, {"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "374e24d21920", "timestamp": 1754062454.0157382, "ttfb": 0.2843680240002868, "duration": 1.491595347000839, "audio_duration": 10.527333333333328, "cancelled": false, "characters_count": 210, "streamed": true, "speech_id": "speech_5f68f62488d5", "error": null}], "eou_metrics": [], "vad_metrics": [{"type": "vad_metrics", "timestamp": 1754062366.093326, "idle_time": 1.5711687299990444, "inference_duration_total": 0.025103240992393694, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062366.0944593, "idle_time": 1.5696257009985857, "inference_duration_total": 0.024569932005761075, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062367.093215, "idle_time": 2.57105716300066, "inference_duration_total": 0.028209346995936357, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062367.093757, "idle_time": 2.5689230939988192, "inference_duration_total": 0.02590092399987043, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062368.1424897, "idle_time": 3.6203318499992747, "inference_duration_total": 0.026046963001135737, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062368.1431644, "idle_time": 3.6183305919985287, "inference_duration_total": 0.030076601011387538, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062369.1438646, "idle_time": 4.621706882999206, "inference_duration_total": 0.02506394900046871, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062369.1446126, "idle_time": 4.619778946998849, "inference_duration_total": 0.024101398998027435, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062370.1926544, "idle_time": 5.670496634000301, "inference_duration_total": 0.022776136996981222, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062370.1932194, "idle_time": 5.668385397999373, "inference_duration_total": 0.022156839002491324, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062371.1933506, "idle_time": 6.668516878999071, "inference_duration_total": 0.022366618999512866, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062371.193529, "idle_time": 6.671370977999686, "inference_duration_total": 0.023567296000692295, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062372.242961, "idle_time": 7.720803038999293, "inference_duration_total": 0.02251682099995378, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062372.2436533, "idle_time": 7.718819447998612, "inference_duration_total": 0.023730725994028035, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062373.242776, "idle_time": 8.720618194000053, "inference_duration_total": 0.021742467000876786, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062373.243622, "idle_time": 8.718788205998862, "inference_duration_total": 0.021689744000468636, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062374.2927508, "idle_time": 9.770593167999323, "inference_duration_total": 0.020749999002873665, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062374.2934127, "idle_time": 9.768578835999506, "inference_duration_total": 0.020965332001651404, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062375.2930515, "idle_time": 10.770893568998872, "inference_duration_total": 0.0247481499955029, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062375.2936742, "idle_time": 10.768840441000066, "inference_duration_total": 0.024694841004020418, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062376.3428514, "idle_time": 11.820693723999284, "inference_duration_total": 0.02546856400294928, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062376.3435376, "idle_time": 11.818703767999978, "inference_duration_total": 0.025851012998828082, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062377.3433032, "idle_time": 12.821145412999613, "inference_duration_total": 0.025070130002859514, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062377.3440187, "idle_time": 12.819184935999147, "inference_duration_total": 0.023616274000232806, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062378.392596, "idle_time": 13.870438250000007, "inference_duration_total": 0.026653917995645315, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062378.3934436, "idle_time": 13.868609851999281, "inference_duration_total": 0.025179352007398847, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062379.393689, "idle_time": 14.86885493599948, "inference_duration_total": 0.029358965000938042, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062379.3939457, "idle_time": 14.87178799100002, "inference_duration_total": 0.025818884998443536, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062380.442496, "idle_time": 15.91766214800009, "inference_duration_total": 0.02190276500004984, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062380.4427586, "idle_time": 15.920600953999383, "inference_duration_total": 0.024165706001440412, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062381.44338, "idle_time": 16.921222535000197, "inference_duration_total": 0.024479124998833868, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062381.4441469, "idle_time": 16.919313089998468, "inference_duration_total": 0.023351613996055676, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062382.4932358, "idle_time": 17.9710778259996, "inference_duration_total": 0.025365342997247353, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062382.4934173, "idle_time": 17.968583384999874, "inference_duration_total": 0.024203177003073506, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062383.4934518, "idle_time": 18.9686179979999, "inference_duration_total": 0.022694607005178113, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062383.4936364, "idle_time": 18.97147858000062, "inference_duration_total": 0.024915408004744677, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062384.543091, "idle_time": 20.02093331099968, "inference_duration_total": 0.025348780005515437, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062384.5438957, "idle_time": 20.019062069999563, "inference_duration_total": 0.02431062299729092, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062385.5424547, "idle_time": 21.020297179999034, "inference_duration_total": 0.024814517999402597, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062385.543203, "idle_time": 21.01836933999948, "inference_duration_total": 0.02477311299480789, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062386.5930872, "idle_time": 22.070929581999735, "inference_duration_total": 0.02494259100421914, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062386.593269, "idle_time": 22.06843513700005, "inference_duration_total": 0.023256517000845633, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062387.5931582, "idle_time": 23.07100052699934, "inference_duration_total": 0.02864180000324268, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062387.5937512, "idle_time": 23.068917582999347, "inference_duration_total": 0.027285181004117476, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062388.6436687, "idle_time": 24.121511036999436, "inference_duration_total": 0.024874103992260643, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062388.643859, "idle_time": 24.11902520900003, "inference_duration_total": 0.02483964200655464, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062389.6433115, "idle_time": 25.12115387099948, "inference_duration_total": 0.026569060995825566, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062389.6441495, "idle_time": 25.11931563199869, "inference_duration_total": 0.026166237992583774, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062390.6935163, "idle_time": 26.16868233399873, "inference_duration_total": 0.023366210001768195, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062390.6937785, "idle_time": 26.17162075699889, "inference_duration_total": 0.025198554996677558, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062391.6922789, "idle_time": 27.170121107999876, "inference_duration_total": 0.02636500499829708, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062391.692855, "idle_time": 27.16802105199895, "inference_duration_total": 0.025258098998165224, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062392.6935325, "idle_time": 28.171374695999475, "inference_duration_total": 0.023105290998501005, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062392.694114, "idle_time": 28.169280328998866, "inference_duration_total": 0.022770913999920595, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062393.7425392, "idle_time": 29.22038154800066, "inference_duration_total": 0.024297461002788623, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062393.7433088, "idle_time": 29.218474876999608, "inference_duration_total": 0.02300512999863713, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062394.7432694, "idle_time": 30.221111576000112, "inference_duration_total": 0.023535815003924654, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062394.7440224, "idle_time": 30.21918846399967, "inference_duration_total": 0.022755118006898556, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062395.7925696, "idle_time": 31.270411801999217, "inference_duration_total": 0.023909206998723675, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062395.7933652, "idle_time": 31.268531423998866, "inference_duration_total": 0.023023110996291507, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062396.7927966, "idle_time": 32.270638957999836, "inference_duration_total": 0.0225893959923269, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062396.7935371, "idle_time": 32.26870317799876, "inference_duration_total": 0.021916210002018488, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062397.8431907, "idle_time": 33.321033033000276, "inference_duration_total": 0.02314686700447055, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062397.8437517, "idle_time": 33.31891778099998, "inference_duration_total": 0.02234191600655322, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062398.8436575, "idle_time": 34.321499637999295, "inference_duration_total": 0.022956867007451365, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062398.844221, "idle_time": 34.319387260999065, "inference_duration_total": 0.023606808001204627, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062399.8929892, "idle_time": 35.370831367999926, "inference_duration_total": 0.024097287001495715, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062399.8938153, "idle_time": 35.3689816419992, "inference_duration_total": 0.022769263003283413, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062400.89372, "idle_time": 36.37156198999946, "inference_duration_total": 0.02371340600257099, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062400.894851, "idle_time": 36.3700172889985, "inference_duration_total": 0.02444135800396907, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062401.942707, "idle_time": 37.42054916300003, "inference_duration_total": 0.024260079000669066, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062401.9429104, "idle_time": 37.41807664999942, "inference_duration_total": 0.023166427001342527, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062402.9432719, "idle_time": 38.42111427800046, "inference_duration_total": 0.024091021999993245, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062402.9439392, "idle_time": 38.419105621998824, "inference_duration_total": 0.023693708000791958, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062403.9923036, "idle_time": 39.47014577799928, "inference_duration_total": 0.024238570000306936, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062403.993076, "idle_time": 39.46824226999888, "inference_duration_total": 0.024031438000747585, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062404.9932988, "idle_time": 40.46846476799874, "inference_duration_total": 0.021835459003341384, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062404.993536, "idle_time": 40.47137832599947, "inference_duration_total": 0.025812772002609563, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062406.0431805, "idle_time": 41.521022728999014, "inference_duration_total": 0.0260031030029495, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062406.0433629, "idle_time": 41.51852899199912, "inference_duration_total": 0.02295487400260754, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062407.0500066, "idle_time": 42.52784888499991, "inference_duration_total": 0.030730078999113175, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062407.0503364, "idle_time": 42.52550249399974, "inference_duration_total": 0.035900563998438884, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062408.0930681, "idle_time": 43.5709103600002, "inference_duration_total": 0.026124491998416488, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062408.093779, "idle_time": 43.568945257999076, "inference_duration_total": 0.0256798330010497, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062409.093253, "idle_time": 44.57109514500007, "inference_duration_total": 0.02530483300688502, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062409.0938654, "idle_time": 44.56903149499885, "inference_duration_total": 0.02503742500266526, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062410.1427004, "idle_time": 45.62054290500055, "inference_duration_total": 0.025251453002056223, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062410.142911, "idle_time": 45.61807706299987, "inference_duration_total": 0.02412782399551361, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062411.143304, "idle_time": 46.621146584999224, "inference_duration_total": 0.02510422000523249, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062411.1441092, "idle_time": 46.61927552999987, "inference_duration_total": 0.023881259006884648, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062412.1926882, "idle_time": 47.67053042099906, "inference_duration_total": 0.024403565996180987, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062412.1934078, "idle_time": 47.66857389999859, "inference_duration_total": 0.024026752997087897, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062413.1926367, "idle_time": 48.67047899099998, "inference_duration_total": 0.025418664010430803, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062413.1933248, "idle_time": 48.66849106799964, "inference_duration_total": 0.025272358005167916, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062414.2433124, "idle_time": 49.72115461700014, "inference_duration_total": 0.02294885599621921, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062414.243505, "idle_time": 49.71867114699853, "inference_duration_total": 0.021225348997177207, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062415.2422645, "idle_time": 50.72010690599927, "inference_duration_total": 0.025071538006159244, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062415.2428362, "idle_time": 50.718002354999044, "inference_duration_total": 0.024256170001535793, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062416.2924006, "idle_time": 51.767566798998814, "inference_duration_total": 0.02270732399847475, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062416.2926614, "idle_time": 51.770503669999016, "inference_duration_total": 0.023197329999675276, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062417.2933948, "idle_time": 52.76856087699889, "inference_duration_total": 0.02238207399750536, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062417.2935817, "idle_time": 52.77142416599963, "inference_duration_total": 0.024293780003063148, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062418.292644, "idle_time": 53.77048628100056, "inference_duration_total": 0.023801431998435874, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062418.293393, "idle_time": 53.76855895399967, "inference_duration_total": 0.023026756001854665, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062419.343251, "idle_time": 54.82109321799908, "inference_duration_total": 0.024667590001627104, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062419.3439722, "idle_time": 54.819138254999416, "inference_duration_total": 0.02404454800489475, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062420.3437417, "idle_time": 55.82158395599981, "inference_duration_total": 0.02227946000311931, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062420.3445168, "idle_time": 55.819683040999735, "inference_duration_total": 0.021933517997240415, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062421.3925903, "idle_time": 56.870432507999794, "inference_duration_total": 0.02539871699991636, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062421.393147, "idle_time": 56.8683130190002, "inference_duration_total": 0.023843749999286956, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062422.3956873, "idle_time": 57.87352976799957, "inference_duration_total": 0.025109604001045227, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062422.396534, "idle_time": 57.87170019499899, "inference_duration_total": 0.025298033999206382, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062423.444424, "idle_time": 58.92226606799886, "inference_duration_total": 0.025748607002242352, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062423.445272, "idle_time": 58.92043808800008, "inference_duration_total": 0.027398410999012413, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062424.4446383, "idle_time": 59.922480461000305, "inference_duration_total": 0.024269306999485707, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062424.4452252, "idle_time": 59.920391308000035, "inference_duration_total": 0.024195202997361775, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062425.492904, "idle_time": 60.96807007699863, "inference_duration_total": 0.021517712999411742, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062425.4930909, "idle_time": 60.970933135999076, "inference_duration_total": 0.02195717200083891, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062426.4929516, "idle_time": 61.97079389499959, "inference_duration_total": 0.025027814001077786, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062426.4935098, "idle_time": 61.96867606599881, "inference_duration_total": 0.022870339000292006, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062427.543003, "idle_time": 63.020845476999966, "inference_duration_total": 0.02592229999936535, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062427.5437925, "idle_time": 63.018958628999826, "inference_duration_total": 0.025615002998165437, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062428.5446498, "idle_time": 64.01981599699866, "inference_duration_total": 0.023908630999358138, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062428.5449142, "idle_time": 64.02275634899888, "inference_duration_total": 0.02488667600300687, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062429.5923538, "idle_time": 65.07019616700018, "inference_duration_total": 0.021800133008582634, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062429.5931375, "idle_time": 65.06830360499953, "inference_duration_total": 0.021355758002755465, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062430.592875, "idle_time": 66.07071720699969, "inference_duration_total": 0.020319181008744636, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062430.5934796, "idle_time": 66.06864572699851, "inference_duration_total": 0.02146615400124574, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062431.642781, "idle_time": 67.12062312299895, "inference_duration_total": 0.02249324199874536, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062431.643287, "idle_time": 67.11845324099886, "inference_duration_total": 0.02146772699779831, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062432.6434064, "idle_time": 68.1212487190005, "inference_duration_total": 0.02119840300110809, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062432.6439707, "idle_time": 68.11913707699932, "inference_duration_total": 0.02116604500770336, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062433.6929781, "idle_time": 69.16814420799892, "inference_duration_total": 0.02270720700471429, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062433.6931727, "idle_time": 69.17101500900026, "inference_duration_total": 0.023930556997584063, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062434.6941023, "idle_time": 70.17194448100054, "inference_duration_total": 0.024215031997300684, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062434.6943738, "idle_time": 70.1695401399993, "inference_duration_total": 0.023665791000894387, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062435.7423158, "idle_time": 71.22015811799974, "inference_duration_total": 0.02105075500367093, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062435.7429872, "idle_time": 71.21815345599862, "inference_duration_total": 0.021931218001554953, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062436.7422557, "idle_time": 72.22009794299993, "inference_duration_total": 0.021481521009263815, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062436.7428198, "idle_time": 72.21798604399919, "inference_duration_total": 0.020902991005641525, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062437.7932973, "idle_time": 73.27113969699894, "inference_duration_total": 0.025084720004088013, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062437.7939963, "idle_time": 73.26916254000025, "inference_duration_total": 0.0245420209976146, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062438.792476, "idle_time": 74.27031803799946, "inference_duration_total": 0.02517814500060922, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062438.7930923, "idle_time": 74.26825853799892, "inference_duration_total": 0.024444516993753496, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062439.8423302, "idle_time": 75.32017247899967, "inference_duration_total": 0.025243016008971608, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062439.8429997, "idle_time": 75.31816601599894, "inference_duration_total": 0.02323150799747964, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062440.843318, "idle_time": 76.32116028599921, "inference_duration_total": 0.024629849005577853, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062440.843938, "idle_time": 76.31910426200011, "inference_duration_total": 0.023291226996661862, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062441.893156, "idle_time": 77.3709982370001, "inference_duration_total": 0.027320358994984417, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062441.8941433, "idle_time": 77.36930945199856, "inference_duration_total": 0.026948318001814187, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062442.892343, "idle_time": 78.37018516599892, "inference_duration_total": 0.028872326000055182, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062442.893124, "idle_time": 78.36829026199848, "inference_duration_total": 0.028476028002842213, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062443.8930514, "idle_time": 79.36821752499964, "inference_duration_total": 0.0231754180003918, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062443.8936145, "idle_time": 79.37145695599975, "inference_duration_total": 0.02512524599842436, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062444.942598, "idle_time": 80.42044025699943, "inference_duration_total": 0.021447900999191916, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062444.943194, "idle_time": 80.418359968, "inference_duration_total": 0.021425265998914256, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062445.9435358, "idle_time": 81.42137816200011, "inference_duration_total": 0.022948302001168486, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062445.9441, "idle_time": 81.41926605200024, "inference_duration_total": 0.02205025599869259, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062446.9934678, "idle_time": 82.4713103289996, "inference_duration_total": 0.02573171499898308, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062446.9943802, "idle_time": 82.46954650399857, "inference_duration_total": 0.023114948999136686, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062447.9935398, "idle_time": 83.47138219800036, "inference_duration_total": 0.02264754600946617, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062447.9944084, "idle_time": 83.46957457799908, "inference_duration_total": 0.020903866998196463, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062449.0422816, "idle_time": 84.52012393400037, "inference_duration_total": 0.021198852993620676, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062449.0429995, "idle_time": 84.518165726, "inference_duration_total": 0.02109974699851591, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062450.0434375, "idle_time": 85.52127991799898, "inference_duration_total": 0.023764758991092094, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062450.0441363, "idle_time": 85.51930225399883, "inference_duration_total": 0.022806408996984828, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062451.0924857, "idle_time": 86.5703279119989, "inference_duration_total": 0.02287430600154039, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062451.0934172, "idle_time": 86.56858343199929, "inference_duration_total": 0.02249442499851284, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062452.0937629, "idle_time": 87.57160512299924, "inference_duration_total": 0.026184996000665706, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062452.0947764, "idle_time": 87.56994264199966, "inference_duration_total": 0.025001036998219206, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062453.143309, "idle_time": 88.6211513140006, "inference_duration_total": 0.02790547399126808, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062453.1434963, "idle_time": 88.61866227700011, "inference_duration_total": 0.025850519001323846, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062454.1436703, "idle_time": 89.61883652399956, "inference_duration_total": 0.029446465998262283, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062454.1439128, "idle_time": 89.6217550170004, "inference_duration_total": 0.030489849004879943, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062455.1924539, "idle_time": 90.67029616000036, "inference_duration_total": 0.02179532999798539, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062455.193266, "idle_time": 90.66843211599917, "inference_duration_total": 0.021865711994905723, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062456.1934683, "idle_time": 91.66863451599966, "inference_duration_total": 0.023482697000872577, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062456.1936505, "idle_time": 91.6714926299992, "inference_duration_total": 0.024605754999356577, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062457.2435122, "idle_time": 92.7186781370001, "inference_duration_total": 0.022730114995283657, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062457.2437031, "idle_time": 92.72154525599944, "inference_duration_total": 0.023800986004061997, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062458.2434015, "idle_time": 93.72124368100049, "inference_duration_total": 0.02483953599767119, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062458.2441266, "idle_time": 93.71929263099992, "inference_duration_total": 0.02315032899787184, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062459.2929637, "idle_time": 94.77080615299928, "inference_duration_total": 0.022676433998640277, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062459.2931457, "idle_time": 94.76831182800015, "inference_duration_total": 0.021796864997668308, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062460.2937737, "idle_time": 95.76893969399862, "inference_duration_total": 0.024194223009544658, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754062460.2941096, "idle_time": 95.77195187299913, "inference_duration_total": 0.02540210000188381, "inference_count": 32, "speech_id": null, "error": null}], "conversation_history": [{"timestamp": "2025-08-01T15:32:44.522312", "role": "system", "content": "Greet the user warmly and ask them how you can help them today about p2p lending."}], "function_calls": [{"timestamp": "2025-08-01T15:32:56.917567", "function_name": "search_knowledge_base", "arguments": {"query": "founders of लेन-देन Club"}, "result": "1. 📌 Question: Who are the founders of LenDenClub?\n💡 Answer: The founders of LenDenClub are <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.\n\n2. 📌 Question: Who founded LenDenClub and in which year?\n💡 Answer: LenDenClu..."}, {"timestamp": "2025-08-01T15:33:08.071474", "function_name": "search_knowledge_base", "arguments": {"query": "manual lending in P2P lending"}, "result": "1. 📌 Question: What is manual lending?\n💡 Answer: In Manual lending - you pick each individual borrower yourself. You can start with just 250 rupees per loan per borrower and choose based on their prof..."}, {"timestamp": "2025-08-01T15:33:22.729996", "function_name": "search_knowledge_base", "arguments": {"query": "who is <PERSON><PERSON><PERSON>"}, "result": "1. 📌 Question: Who is <PERSON><PERSON><PERSON>?\n💡 Answer: <PERSON><PERSON><PERSON> is the Co-Founder and CEO of LenDenClub, pioneering India's leading P2P lending platform, driving financial inclusion through fintech innovat..."}, {"timestamp": "2025-08-01T15:33:40.212050", "function_name": "search_knowledge_base", "arguments": {"query": "Dipesh Karki profile"}, "result": "1. 📌 Question: What prior experience does <PERSON><PERSON><PERSON> have?\n💡 Answer: <PERSON><PERSON><PERSON> previously led software development at IDA Business Solutions and Newgen Software, underpinned by a B.Tech in ECE from NIT..."}, {"timestamp": "2025-08-01T15:33:59.162918", "function_name": "search_knowledge_base", "arguments": {"query": "lumpsum in P2P lending"}, "result": "1. 📌 Question: What is Lumpsum lending?\n💡 Answer: Lumpsum is a lending option that allows you to lend large amounts, starting from ₹25,000 up to ₹25,00,000 under one scheme in one go.\n\n2. 📌 Question: ..."}], "usage_summary": "UsageSummary(llm_prompt_tokens=16205, llm_prompt_cached_tokens=8704, llm_completion_tokens=497, tts_characters_count=1555, tts_audio_duration=86.23133333333324, stt_audio_duration=0.0)"}