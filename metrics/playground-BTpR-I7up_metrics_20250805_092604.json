{"session_info": {"session_name": "playground-BTpR-I7up", "start_time": "2025-08-05T09:23:08.483147", "end_time": "2025-08-05T09:26:04.635274", "duration_seconds": 176.152127}, "structured_conversation": [{"assistant_turn": {"timestamp": "2025-08-05T09:23:19.614402", "content": "Hello! Main <PERSON><PERSON><PERSON> hoon, aap kaise hain? Kya aapko P2P lending ke baare mein kuch jaanana hai? Main yahan aapki madad karne ke liye hoon.", "function_calls": [], "metrics": {"llm": [{"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C18AvrfIateM59T3ofyL7pgoqeEM2", "timestamp": "2025-08-05T09:23:12.093285", "duration": 3.4675849140003265, "ttft": 1.2092570969980443, "cancelled": false, "completion_tokens": 49, "prompt_tokens": 1068, "prompt_cached_tokens": 0, "total_tokens": 1117, "tokens_per_second": 14.13087241271675, "speech_id": null}], "tts": [{"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "de51b986557b", "timestamp": "2025-08-05T09:23:12.530223", "ttfb": 0.2905644409984234, "duration": 1.9949286239971116, "audio_duration": 8.787333333333335, "cancelled": false, "characters_count": 138, "streamed": true, "segment_id": "adbfc632f0fc", "speech_id": null}]}}}, {"user_turn": {"timestamp": "2025-08-05T09:23:24.879504", "content": "Hi, how are you today?", "metrics": {"stt": [{"type": "stt_metrics", "label": "livekit.plugins.openai.stt.STT", "request_id": "", "timestamp": "2025-08-05T09:23:24.870486", "duration": 0.6438931340017007, "audio_duration": 1.812, "streamed": false}], "eou": []}}, "assistant_turn": {"timestamp": "2025-08-05T09:23:33.607529", "content": "I'm doing well, thank you! Aur aap kaise hain? Kya aap P2P lending ya लेन-देन Club ke baare mein kuch jaanana chahte hain?", "function_calls": [], "metrics": {"llm": [{"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C18BBMsA5yV8ODCKsL7zEVHG5kzvd", "timestamp": "2025-08-05T09:23:26.270253", "duration": 1.396945890999632, "ttft": 0.8023761139993439, "cancelled": false, "completion_tokens": 41, "prompt_tokens": 1114, "prompt_cached_tokens": 0, "total_tokens": 1155, "tokens_per_second": 29.349740934246967, "speech_id": null}], "tts": [{"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "8ff9220097d9", "timestamp": "2025-08-05T09:23:27.206187", "ttfb": 0.25520349300131784, "duration": 1.4418988159995934, "audio_duration": 7.587333333333337, "cancelled": false, "characters_count": 122, "streamed": true, "segment_id": "34069db7d5e1", "speech_id": null}]}}}, {"user_turn": {"timestamp": "2025-08-05T09:23:39.880080", "content": "Tell me about founders.", "metrics": {"stt": [{"type": "stt_metrics", "label": "livekit.plugins.openai.stt.STT", "request_id": "", "timestamp": "2025-08-05T09:23:39.871054", "duration": 0.9464470070015523, "audio_duration": 1.812, "streamed": false}], "eou": []}}, "assistant_turn": {"timestamp": "2025-08-05T09:23:54.258969", "content": "लेन-देन Club ke founders <PERSON><PERSON><PERSON> aur <PERSON>in, jinh<PERSON> isse 2015 mein shuru kiya tha. Kya aapko unki kahani ya P2P lending ke baare mein aur jaanana hai?", "function_calls": [{"timestamp": "2025-08-05T09:23:42.620350", "function_name": "search_knowledge_base", "arguments": {"query": "founders of लेन-देन Club"}, "result": "1. 📌 Question: Who are the founders of LenDenClub?\n💡 Answer: The founders of LenDenClub are <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.\n2. 📌 Question: Who founded LenDenClub and in which year?\n💡 Answer: LenDenClub..."}], "metrics": {"llm": [{"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C18BQi2yqg2QCJQok4VbeQvBwDNH8", "timestamp": "2025-08-05T09:23:40.991433", "duration": 1.117814614000963, "ttft": 1.1136000190017512, "cancelled": false, "completion_tokens": 23, "prompt_tokens": 1167, "prompt_cached_tokens": 0, "total_tokens": 1190, "tokens_per_second": 20.57586268055374, "speech_id": null}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C18BSHEqgpJcbvM8oI7OWixghMVCt", "timestamp": "2025-08-05T09:23:44.198878", "duration": 1.5775565530020685, "ttft": 0.7495941670022148, "cancelled": false, "completion_tokens": 56, "prompt_tokens": 1274, "prompt_cached_tokens": 1024, "total_tokens": 1330, "tokens_per_second": 35.49793501439474, "speech_id": null}], "tts": [{"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "646e6a1c6877", "timestamp": "2025-08-05T09:23:45.764090", "ttfb": 0.8832716640026774, "duration": 1.8970147899999574, "audio_duration": 9.507333333333332, "cancelled": false, "characters_count": 167, "streamed": true, "segment_id": "45f1d82f8e5e", "speech_id": null}]}}}, {"user_turn": {"timestamp": "2025-08-05T09:24:06.312098", "content": "Nice. Do you know anything about manual lending?", "metrics": {"stt": [{"type": "stt_metrics", "label": "livekit.plugins.openai.stt.STT", "request_id": "", "timestamp": "2025-08-05T09:24:06.302677", "duration": 0.8781751319984323, "audio_duration": 3.124, "streamed": false}], "eou": []}}, "assistant_turn": {"timestamp": "2025-08-05T09:24:29.676947", "content": "Manual lending ka matlab hai ki aap khud har borrower ko chun sakte hain. Aap sirf two hundred fifty rupees se shuru kar sakte hain aur borrowers ke profile, income aur doosri details ke adhar par chunav kar sakte hain. Ye aapko borrowers par poora control deta hai. Ye feature लेन-देन Club par October 2023 mein launch hua tha. Kya aap iske baare mein aur", "function_calls": [{"timestamp": "2025-08-05T09:24:08.396399", "function_name": "search_knowledge_base", "arguments": {"query": "manual lending in P2P"}, "result": "1. 📌 Question: What is manual lending?\n💡 Answer: In Manual lending - you pick each individual borrower yourself. You can start with just 250 rupees per loan per borrower and choose based on their prof..."}], "metrics": {"llm": [{"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C18BquG0gjP5eA1K4ziYbOd3Hx1Cn", "timestamp": "2025-08-05T09:24:07.239270", "duration": 0.9339699669981201, "ttft": 0.9299863140004163, "cancelled": false, "completion_tokens": 21, "prompt_tokens": 1347, "prompt_cached_tokens": 1280, "total_tokens": 1368, "tokens_per_second": 22.484663042748856, "speech_id": null}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C18Bs1yivWMmJfSKibd3flaXpxC94", "timestamp": "2025-08-05T09:24:10.847398", "duration": 2.450089811998623, "ttft": 0.7258763690006163, "cancelled": false, "completion_tokens": 101, "prompt_tokens": 1481, "prompt_cached_tokens": 1280, "total_tokens": 1582, "tokens_per_second": 41.22297864567291, "speech_id": null}], "tts": [{"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "88e05ca8c91a", "timestamp": "2025-08-05T09:24:12.462436", "ttfb": 0.3006555930005561, "duration": 3.0846231579998857, "audio_duration": 20.72733333333329, "cancelled": false, "characters_count": 373, "streamed": true, "segment_id": "f9ca9d95f581", "speech_id": null}]}}}, {"user_turn": {"timestamp": "2025-08-05T09:24:31.707634", "content": "پھر میں وہ تم سے ملنے۔", "metrics": {"stt": [{"type": "stt_metrics", "label": "livekit.plugins.openai.stt.STT", "request_id": "", "timestamp": "2025-08-05T09:24:31.697361", "duration": 0.8697478930007492, "audio_duration": 2.164, "streamed": false}], "eou": []}}, "assistant_turn": {"timestamp": "2025-08-05T09:24:40.576355", "content": "Mujhe aap se milne ka intezaar rahega! Kya aapko P2P lending ya लेन-देन Club ke baare mein aur kuch jaana hai? Main yahan aapki", "function_calls": [], "metrics": {"llm": [{"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C18CGBRQ7Bp3qyKmkCIdbfljpvhZC", "timestamp": "2025-08-05T09:24:34.321925", "duration": 2.621875774999353, "ttft": 0.7581996459994116, "cancelled": false, "completion_tokens": 57, "prompt_tokens": 1592, "prompt_cached_tokens": 1536, "total_tokens": 1649, "tokens_per_second": 21.740160439147452, "speech_id": null}], "tts": [{"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "fadbcac43127", "timestamp": "2025-08-05T09:24:34.725655", "ttfb": 0.3214743610005826, "duration": 2.028004499999952, "audio_duration": 8.487333333333336, "cancelled": false, "characters_count": 153, "streamed": true, "segment_id": "042291d1ae19", "speech_id": null}]}}}, {"user_turn": {"timestamp": "2025-08-05T09:24:43.524397", "content": "Tell me about lumpsum lending.", "metrics": {"stt": [{"type": "stt_metrics", "label": "livekit.plugins.openai.stt.STT", "request_id": "", "timestamp": "2025-08-05T09:24:43.514387", "duration": 0.8392463829986809, "audio_duration": 3.124, "streamed": false}], "eou": []}}, "assistant_turn": {"timestamp": "2025-08-05T09:25:02.076937", "content": "Lumpsum lending ek aisa option hai jisme aap ek saath bada amount lend kar sakte hain, jo ki twenty five thousand rupees se lekar twenty five lakh rupees tak ho sakta hai. Ye amount multiple borrowers mein diversify hota hai, jisse aapka individual risk exposure kam ho jata hai.", "function_calls": [{"timestamp": "2025-08-05T09:24:45.251471", "function_name": "search_knowledge_base", "arguments": {"query": "lumpsum lending in P2P"}, "result": "1. 📌 Question: What is Lumpsum lending?\n💡 Answer: Lumpsum is a lending option that allows you to lend large amounts, starting from ₹25,000 up to ₹25,00,000 under one scheme in one go.\n2. 📌 Question: W..."}], "metrics": {"llm": [{"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C18CRDeDLAWolGZYvUTEfwmssl3E7", "timestamp": "2025-08-05T09:24:44.422683", "duration": 0.9059327840004698, "ttft": 0.9018953220002004, "cancelled": false, "completion_tokens": 23, "prompt_tokens": 1653, "prompt_cached_tokens": 1152, "total_tokens": 1676, "tokens_per_second": 25.38819701218371, "speech_id": null}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C18CTY9fTx9tm0cSvZkkK0GyGjX1y", "timestamp": "2025-08-05T09:24:47.528877", "duration": 2.276489419000427, "ttft": 0.8448432869990938, "cancelled": false, "completion_tokens": 107, "prompt_tokens": 1811, "prompt_cached_tokens": 1536, "total_tokens": 1918, "tokens_per_second": 47.002195181290205, "speech_id": null}], "tts": [{"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "b3a80ff4182f", "timestamp": "2025-08-05T09:24:49.992208", "ttfb": 0.35139523899852065, "duration": 3.2301069409986667, "audio_duration": 22.887333333333284, "cancelled": false, "characters_count": 427, "streamed": true, "segment_id": "efd77125b339", "speech_id": null}]}}}, {"user_turn": {"timestamp": "2025-08-05T09:25:04.065716", "content": "تمیق ابوٹ دپیش", "metrics": {"stt": [{"type": "stt_metrics", "label": "livekit.plugins.openai.stt.STT", "request_id": "", "timestamp": "2025-08-05T09:25:04.055594", "duration": 0.7303311059986299, "audio_duration": 2.292, "streamed": false}], "eou": []}}, "assistant_turn": {"timestamp": "2025-08-05T09:25:23.484495", "content": "Dipesh Karki ne pehle IDA Business Solutions aur Newgen Software mein software development lead kiya hai. Unka educational background Electronics and Communication Engineering mein Bachelor of Technology hai, jo unhone NIT Kurukshetra se kiya. Kya aap unke baare mein aur kuch jaanana chahte hain?", "function_calls": [{"timestamp": "2025-08-05T09:25:06.316325", "function_name": "search_knowledge_base", "arguments": {"query": "Dipesh Karki"}, "result": "1. 📌 Question: What prior experience does <PERSON><PERSON><PERSON> have?\n💡 Answer: <PERSON><PERSON><PERSON> previously led software development at IDA Business Solutions and Newgen Software, underpinned by a B.Tech in ECE from NIT..."}], "metrics": {"llm": [{"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C18CmNXK6Hwc01Su1enpFvHCMdW8T", "timestamp": "2025-08-05T09:25:05.113937", "duration": 1.0559008669988543, "ttft": 1.051456960998621, "cancelled": false, "completion_tokens": 19, "prompt_tokens": 1895, "prompt_cached_tokens": 1792, "total_tokens": 1914, "tokens_per_second": 17.99411345688441, "speech_id": null}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C18CorvMNaFUcvKxiNUs1j2KrrOEc", "timestamp": "2025-08-05T09:25:08.169073", "duration": 1.851771542998904, "ttft": 0.7686094050004613, "cancelled": false, "completion_tokens": 69, "prompt_tokens": 2040, "prompt_cached_tokens": 1792, "total_tokens": 2109, "tokens_per_second": 37.261615916321944, "speech_id": null}], "tts": [{"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "ffdcf4cf6739", "timestamp": "2025-08-05T09:25:09.738986", "ttfb": 0.33332689400049276, "duration": 2.2771135060029337, "audio_duration": 15.68733333333331, "cancelled": false, "characters_count": 297, "streamed": true, "segment_id": "7a546c51aaa8", "speech_id": null}]}}}, {"user_turn": {"timestamp": "2025-08-05T09:25:32.542920", "content": "Okay, bye.", "metrics": {"stt": [{"type": "stt_metrics", "label": "livekit.plugins.openai.stt.STT", "request_id": "", "timestamp": "2025-08-05T09:25:32.531910", "duration": 0.5070030140013841, "audio_duration": 1.62, "streamed": false}], "eou": []}}, "assistant_turn": {"timestamp": "2025-08-05T09:25:42.046875", "content": "Goodbye! <PERSON><PERSON> a<PERSON>ko kabhi bhi P2P lending ya लेन-देन Club ke baare mein jaanana ho, to z<PERSON>r wa<PERSON> aaiyega. Take care!", "function_calls": [], "metrics": {"llm": [{"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C18DEZLG7DMgyaevoWootUdVXwvbN", "timestamp": "2025-08-05T09:25:33.860195", "duration": 1.325671106002119, "ttft": 0.7022949490019528, "cancelled": false, "completion_tokens": 42, "prompt_tokens": 2120, "prompt_cached_tokens": 2048, "total_tokens": 2162, "tokens_per_second": 31.682066396288242, "speech_id": null}], "tts": [{"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "a845572f587b", "timestamp": "2025-08-05T09:25:35.126902", "ttfb": 0.4200529150002694, "duration": 1.2697201530027087, "audio_duration": 7.767333333333338, "cancelled": false, "characters_count": 119, "streamed": true, "segment_id": "70de3b808e2b", "speech_id": null}]}}}], "usage_summary": "UsageSummary(llm_prompt_tokens=18562, llm_prompt_cached_tokens=13440, llm_completion_tokens=608, tts_characters_count=1796, tts_audio_duration=101.43866666666658, stt_audio_duration=15.948)"}