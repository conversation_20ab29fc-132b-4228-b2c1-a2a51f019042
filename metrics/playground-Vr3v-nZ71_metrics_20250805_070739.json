{"session_info": {"session_name": "playground-Vr3v-nZ71", "start_time": "2025-08-05T07:05:47.842332", "end_time": "2025-08-05T07:07:39.136691", "duration_seconds": 111.294359}, "structured_conversation": [{"assistant_turn": {"timestamp": "2025-08-05T07:05:53.918575", "content": "Hello! Main <PERSON><PERSON><PERSON> hoon, aap kaise hain? <PERSON><PERSON> a<PERSON> to P", "function_calls": [], "metrics": {"llm": [{"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C1621YVYfq36t2w0DLwOHhgv4jLPX", "timestamp": "2025-08-05T07:05:50.257035", "duration": 2.2359871190019476, "ttft": 1.437115659002302, "cancelled": false, "completion_tokens": 33, "prompt_tokens": 1071, "total_tokens": 1104, "tokens_per_second": 14.758582336882977}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "", "timestamp": "2025-08-05T07:05:56.469121", "duration": 0.5496165810000093, "ttft": -1.0, "cancelled": true, "completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0, "tokens_per_second": 0.0}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "", "timestamp": "2025-08-05T07:05:58.367209", "duration": 0.2558968610028387, "ttft": -1.0, "cancelled": true, "completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0, "tokens_per_second": 0.0}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "", "timestamp": "2025-08-05T07:05:59.918977", "duration": 0.48679882000215, "ttft": -1.0, "cancelled": true, "completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0, "tokens_per_second": 0.0}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "", "timestamp": "2025-08-05T07:06:01.068052", "duration": 0.46237566100171534, "ttft": -1.0, "cancelled": true, "completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0, "tokens_per_second": 0.0}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "", "timestamp": "2025-08-05T07:06:03.987923", "duration": 1.4774694740008272, "ttft": -1.0, "cancelled": true, "completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0, "tokens_per_second": 0.0}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C162GU84pZhZT4mUCvvSgjSeiY7AH", "timestamp": "2025-08-05T07:06:05.224370", "duration": 1.2443692600027134, "ttft": 0.5674703940021573, "cancelled": false, "completion_tokens": 38, "prompt_tokens": 1125, "total_tokens": 1163, "tokens_per_second": 30.5375592450083}], "tts": [{"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "14abc08d60be", "timestamp": "2025-08-05T07:06:06.020443", "ttfb": 0.2497103200003039, "duration": 1.0007294280003407, "audio_duration": 6.087333333333336, "cancelled": false, "characters_count": 109, "streamed": true, "speech_id": "speech_f4aad7d4f362", "error": null}]}}}, {"user_turn": {"timestamp": "2025-08-05T07:06:03.986260", "content": "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "metrics": {"stt": [], "vad": [], "eou": []}}, "assistant_turn": {"timestamp": "2025-08-05T07:06:11.357753", "content": "Aap kaise hain? <PERSON>ya aapko P to P lending ke baare mein kuch jaan na hai? Main madad karne ke liye yahan hoon!", "function_calls": [], "metrics": {"llm": [{"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C162ZeB9qfBtwvJpk5BWUiNkSKoFM", "timestamp": "2025-08-05T07:06:24.907093", "duration": 1.2930272209996474, "ttft": 0.60435570700065, "cancelled": false, "completion_tokens": 42, "prompt_tokens": 1182, "total_tokens": 1224, "tokens_per_second": 32.48191477943484}], "tts": [{"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "8365a0864980", "timestamp": "2025-08-05T07:06:25.799124", "ttfb": 0.258602696001617, "duration": 1.3642275300007896, "audio_duration": 6.9873333333333365, "cancelled": false, "characters_count": 115, "streamed": true, "speech_id": "speech_f0877fb937d0", "error": null}]}}}, {"user_turn": {"timestamp": "2025-08-05T07:06:23.621612", "content": "What's going on with you? How are you doing today?", "metrics": {"stt": [], "vad": [], "eou": []}}, "assistant_turn": {"timestamp": "2025-08-05T07:06:31.681965", "content": "Main theek hoon, shu<PERSON><PERSON>! Aap kaise hain? Kya aapko P to P lending ya लेन-देन Club ke baare mein kuch jaan na hai?", "function_calls": [], "metrics": {"llm": [{"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C162t9gawByWcpXAwacOcyKA4pXUC", "timestamp": "2025-08-05T07:06:44.208076", "duration": 1.0852426779965754, "ttft": 1.0727011549970484, "cancelled": false, "completion_tokens": 23, "prompt_tokens": 1236, "total_tokens": 1259, "tokens_per_second": 21.193416427797892}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C162vFO9XY257oQD3oZQ0fazZl2r2", "timestamp": "2025-08-05T07:06:46.522643", "duration": 1.3756173670008138, "ttft": 0.7427564079989679, "cancelled": false, "completion_tokens": 48, "prompt_tokens": 1343, "total_tokens": 1391, "tokens_per_second": 34.89342396472638}], "tts": [{"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "fa0867e75c1b", "timestamp": "2025-08-05T07:06:47.865418", "ttfb": 0.8738365680001152, "duration": 1.7460304209998867, "audio_duration": 8.427333333333335, "cancelled": false, "characters_count": 151, "streamed": true, "speech_id": "speech_5c3f7a3a7257", "error": null}]}}}, {"user_turn": {"timestamp": "2025-08-05T07:06:43.131010", "content": "Tell me about founders.", "metrics": {"stt": [], "vad": [], "eou": []}}, "assistant_turn": {"timestamp": "2025-08-05T07:06:54.916677", "content": "लेन-देन Club ke founders <PERSON><PERSON><PERSON> aur <PERSON>in. Inhone is platform ko 2015 mein shuru kiya tha. Kya aapko unke baare mein aur", "function_calls": [{"timestamp": "2025-08-05T07:06:45.145846", "function_name": "search_knowledge_base", "arguments": {"query": "founders of लेन-देन Club"}, "result": "1. 📌 Question: Who are the founders of LenDenClub?\n💡 Answer: The founders of LenDenClub are <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.\n\n2. 📌 Question: Who founded LenDenClub and in which year?\n💡 Answer: LenDenClu..."}], "metrics": {"llm": [{"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C1638dVvPsBWSFId1VclUv97xGET4", "timestamp": "2025-08-05T07:06:59.282048", "duration": 1.5633654679986648, "ttft": 1.4088843509998696, "cancelled": true, "completion_tokens": 0, "prompt_tokens": 0, "total_tokens": 0, "tokens_per_second": 0.0}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C1639R4rIw7XDbzxTqnpx6NgKGw3y", "timestamp": "2025-08-05T07:07:00.349626", "duration": 1.0804658269989886, "ttft": 1.0797945080012141, "cancelled": false, "completion_tokens": 17, "prompt_tokens": 1410, "total_tokens": 1427, "tokens_per_second": 15.733954351168862}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C163B40wAnWoYun957hL4BHfmGBT0", "timestamp": "2025-08-05T07:07:03.205003", "duration": 1.8099365470006887, "ttft": 0.5349413020012435, "cancelled": false, "completion_tokens": 83, "prompt_tokens": 1559, "total_tokens": 1642, "tokens_per_second": 45.85796122938249}], "tts": [{"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "64452aeb1767", "timestamp": "2025-08-05T07:07:04.667594", "ttfb": 0.2784716819987807, "duration": 2.4343417009986297, "audio_duration": 16.707333333333306, "cancelled": false, "characters_count": 317, "streamed": true, "speech_id": "speech_cb977d28d964", "error": null}]}}}, {"user_turn": {"timestamp": "2025-08-05T07:06:59.277453", "content": "Do you know anything about manual lending?", "metrics": {"stt": [], "vad": [], "eou": []}}, "assistant_turn": {"timestamp": "2025-08-05T07:07:11.466773", "content": "Manual lending ka matlab hai ki aap har individual borrower ko khud select karte hain. Aap sirf two hundred fifty rupees se shuru kar sakte hain aur borrower ke profile, income,", "function_calls": [{"timestamp": "2025-08-05T07:07:01.393993", "function_name": "search_knowledge_base", "arguments": {"query": "manual lending"}, "result": "1. 📌 Question: What is manual lending?\n💡 Answer: In Manual lending - you pick each individual borrower yourself. You can start with just 250 rupees per loan per borrower and choose based on their prof..."}], "metrics": {"llm": [{"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C163MxRdk9vgR70pDPzddogF3TDND", "timestamp": "2025-08-05T07:07:13.408305", "duration": 1.1579031109977223, "ttft": 0.5381058029997803, "cancelled": false, "completion_tokens": 40, "prompt_tokens": 1611, "total_tokens": 1651, "tokens_per_second": 34.54520470675088}], "tts": [{"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "ff54ddd8076b", "timestamp": "2025-08-05T07:07:14.607594", "ttfb": 0.25292133100083447, "duration": 1.2073117350009852, "audio_duration": 7.527333333333337, "cancelled": false, "characters_count": 117, "streamed": true, "speech_id": "speech_772fe05ce3bf", "error": null}]}}}, {"user_turn": {"timestamp": "2025-08-05T07:07:12.259459", "content": "Bye.", "metrics": {"stt": [], "vad": [], "eou": []}}, "assistant_turn": {"timestamp": "2025-08-05T07:07:15.652272", "content": "Alvida! Agar aapko kabhi bhi P to", "function_calls": [], "metrics": {"llm": [], "tts": []}}}], "usage_summary": "UsageSummary(llm_prompt_tokens=10537, llm_prompt_cached_tokens=8576, llm_completion_tokens=324, tts_characters_count=906, tts_audio_duration=51.94399999999998, stt_audio_duration=19.291999999999998)"}