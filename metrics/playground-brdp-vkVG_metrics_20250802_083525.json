{"session_info": {"session_name": "playground-brdp-vkVG", "start_time": "2025-08-02T08:32:30.622494", "end_time": "2025-08-02T08:35:25.471135", "duration_seconds": 174.848641}, "llm_metrics": [{"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C01xHSn7vCHpVUZs0gCh8lMwjbM0z", "timestamp": 1754123552.5058935, "duration": 1.7600719990005018, "ttft": 1.1399268269997265, "cancelled": false, "completion_tokens": 48, "prompt_tokens": 1071, "total_tokens": 1119, "tokens_per_second": 27.271611631375265}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C01xU6m8hgETP1hqY77goA1PZ2cIA", "timestamp": 1754123565.2794118, "duration": 1.1196642149989202, "ttft": 0.7233090939989779, "cancelled": false, "completion_tokens": 41, "prompt_tokens": 1109, "total_tokens": 1150, "tokens_per_second": 36.61812126418592}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C01xflT5hbZOMIc5lGLkx6aJLlvW0", "timestamp": 1754123575.987995, "duration": 1.0635299450004823, "ttft": 0.9331631940003717, "cancelled": false, "completion_tokens": 23, "prompt_tokens": 1161, "total_tokens": 1184, "tokens_per_second": 21.626095351729443}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C01xhCusnJc2WR9mCcUXKWVCOrcNm", "timestamp": 1754123578.9128797, "duration": 1.6680830319965025, "ttft": 0.9901699619986175, "cancelled": false, "completion_tokens": 55, "prompt_tokens": 1268, "total_tokens": 1323, "tokens_per_second": 32.97197977859133}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C01xnXiO6jy6MegAIXUvcs6OljXIZ", "timestamp": 1754123584.4464998, "duration": 1.5818142440002703, "ttft": 1.3267214539991983, "cancelled": false, "completion_tokens": 61, "prompt_tokens": 1170, "total_tokens": 1231, "tokens_per_second": 38.56331439128802}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C01xpXcrqw1pEF8HKa91qFewgX5hU", "timestamp": 1754123586.860317, "duration": 1.4217563349993725, "ttft": 0.6058593009984179, "cancelled": false, "completion_tokens": 109, "prompt_tokens": 1490, "total_tokens": 1599, "tokens_per_second": 76.66573892920132}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C01y5eZzeNKgdcy312wgcS67RNRbi", "timestamp": 1754123602.9336205, "duration": 1.6948787040018942, "ttft": 0.6844954700027301, "cancelled": false, "completion_tokens": 100, "prompt_tokens": 1304, "total_tokens": 1404, "tokens_per_second": 59.00127234113164}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C01yETgqvYTpyl6YMedxN5kjSDhe0", "timestamp": 1754123611.6849408, "duration": 1.3459702470026969, "ttft": 0.5382089550003002, "cancelled": false, "completion_tokens": 74, "prompt_tokens": 1707, "total_tokens": 1781, "tokens_per_second": 54.978927034077095}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C01yQSN9lKGzCievLGmJmOdlKDmvI", "timestamp": 1754123622.8840683, "duration": 0.9644503790004819, "ttft": 0.5998682829995232, "cancelled": false, "completion_tokens": 38, "prompt_tokens": 1749, "total_tokens": 1787, "tokens_per_second": 39.400679213151115}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C01yZstb1mTCES3I6kgK5TVRprtCr", "timestamp": 1754123631.8351412, "duration": 0.9271053560005384, "ttft": 0.8214675489980436, "cancelled": false, "completion_tokens": 19, "prompt_tokens": 1852, "total_tokens": 1871, "tokens_per_second": 20.493895194354767}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C01ybELjzDSC1zx6EDAFFRcvLSyrK", "timestamp": 1754123634.168402, "duration": 1.1542350329982582, "ttft": 0.7847952320007607, "cancelled": false, "completion_tokens": 58, "prompt_tokens": 1997, "total_tokens": 2055, "tokens_per_second": 50.249731070229544}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C01ylT2GH3E72HudnLRewZBqlV5Yo", "timestamp": 1754123644.7777457, "duration": 1.1742480079992674, "ttft": 0.6400581170018995, "cancelled": false, "completion_tokens": 45, "prompt_tokens": 1860, "total_tokens": 1905, "tokens_per_second": 38.32239841451626}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C01zOdRFQ4l9YYttHrtPfILafVm2A", "timestamp": 1754123683.2793884, "duration": 1.063987396999437, "ttft": 0.562560301998019, "cancelled": false, "completion_tokens": 26, "prompt_tokens": 2120, "total_tokens": 2146, "tokens_per_second": 24.43637967265674}, {"type": "llm_metrics", "label": "livekit.plugins.openai.llm.LLM", "request_id": "chatcmpl-C01zjBxWvESvUUcmSd0kc6icSbqD1", "timestamp": 1754123704.1902401, "duration": 1.3721201320004184, "ttft": 0.966419805001351, "cancelled": false, "completion_tokens": 38, "prompt_tokens": 2149, "total_tokens": 2187, "tokens_per_second": 27.69436809049633}], "stt_metrics": [], "tts_metrics": [{"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "112262de7f4c", "timestamp": 1754123553.4963396, "ttfb": 0.353559007999138, "duration": 1.3214849500000128, "audio_duration": 8.487333333333336, "cancelled": false, "characters_count": 141, "streamed": true, "speech_id": "speech_dffc173e9c74", "error": null}, {"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "3c788ea8ce48", "timestamp": 1754123566.2082667, "ttfb": 0.2755851000001712, "duration": 1.2254433179987245, "audio_duration": 7.527333333333337, "cancelled": false, "characters_count": 108, "streamed": true, "speech_id": "speech_8ac87e05de37", "error": null}, {"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "6790f97bf41b", "timestamp": 1754123580.3880422, "ttfb": 0.8548856560009881, "duration": 1.8897196130019438, "audio_duration": 9.747333333333332, "cancelled": false, "characters_count": 171, "streamed": true, "speech_id": "speech_fa3cfca23908", "error": null}, {"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "ebd32113baa5", "timestamp": 1754123589.4385037, "ttfb": 0.3616934300007415, "duration": 3.1315465890002088, "audio_duration": 22.047333333333288, "cancelled": false, "characters_count": 412, "streamed": true, "speech_id": "speech_7c31b57a396f", "error": null}, {"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "848d0984ba14", "timestamp": 1754123605.1317244, "ttfb": 0.25684748200001195, "duration": 3.0434210960011114, "audio_duration": 21.50733333333329, "cancelled": false, "characters_count": 391, "streamed": true, "speech_id": "speech_851707b1636e", "error": null}, {"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "5725fc536c30", "timestamp": 1754123613.5035276, "ttfb": 0.25578830300219124, "duration": 2.1161786900011066, "audio_duration": 14.727333333333313, "cancelled": false, "characters_count": 260, "streamed": true, "speech_id": "speech_5de79b06b3c5", "error": null}, {"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "33838560847c", "timestamp": 1754123623.8558905, "ttfb": 0.3388919260032708, "duration": 1.033616829001403, "audio_duration": 6.327333333333336, "cancelled": false, "characters_count": 108, "streamed": true, "speech_id": "speech_0ae624331289", "error": null}, {"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "8090b6a1fc97", "timestamp": 1754123636.2727349, "ttfb": 0.44378558499738574, "duration": 2.1036949699991965, "audio_duration": 14.547333333333315, "cancelled": false, "characters_count": 248, "streamed": true, "speech_id": "speech_e2722c144f4a", "error": null}, {"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "2d7a9c28d6dd", "timestamp": 1754123645.8364882, "ttfb": 0.37612708899905556, "duration": 1.1683691690013802, "audio_duration": 7.167333333333337, "cancelled": false, "characters_count": 123, "streamed": true, "speech_id": "speech_1479baa95a4e", "error": null}, {"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "8e2e2c620712", "timestamp": 1754123684.0130663, "ttfb": 0.252504231997591, "duration": 0.7385693369978981, "audio_duration": 4.2873333333333346, "cancelled": false, "characters_count": 70, "streamed": true, "speech_id": "speech_fb97ac6c0363", "error": null}, {"type": "tts_metrics", "label": "livekit.plugins.google.tts.TTS", "request_id": "18d760a598b6", "timestamp": 1754123705.005505, "ttfb": 0.2871037270015222, "duration": 1.1468653970005107, "audio_duration": 6.207333333333336, "cancelled": false, "characters_count": 108, "streamed": true, "speech_id": "speech_0e683dc35012", "error": null}], "eou_metrics": [], "vad_metrics": [{"type": "vad_metrics", "timestamp": 1754123552.1237159, "idle_time": 1.3815398480001022, "inference_duration_total": 0.02906772000278579, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123552.1248055, "idle_time": 1.3799227370000153, "inference_duration_total": 0.027931871998589486, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123553.1236365, "idle_time": 2.381460002001404, "inference_duration_total": 0.028865435993793653, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123553.1241543, "idle_time": 2.379271413999959, "inference_duration_total": 0.02833801300948835, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123554.173577, "idle_time": 3.431400477002171, "inference_duration_total": 0.03310544800478965, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123554.173759, "idle_time": 3.4288758319999033, "inference_duration_total": 0.025025632992765168, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123555.1733758, "idle_time": 4.431199402002676, "inference_duration_total": 0.024565657000493957, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123555.1738427, "idle_time": 4.428959468998073, "inference_duration_total": 0.02368719001970021, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123556.2229378, "idle_time": 5.4807613370030595, "inference_duration_total": 0.023245584015967324, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123556.2234623, "idle_time": 5.478579190999881, "inference_duration_total": 0.02323674699073308, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123557.22342, "idle_time": 6.478536749000341, "inference_duration_total": 0.023003828009677818, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123557.2236083, "idle_time": 6.48143176099984, "inference_duration_total": 0.02412012000422692, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123558.273398, "idle_time": 7.531221559002006, "inference_duration_total": 0.024107336012093583, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123558.2739031, "idle_time": 7.529020017998846, "inference_duration_total": 0.022753945009753807, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123559.274118, "idle_time": 8.531941585002642, "inference_duration_total": 0.02739494798515807, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123559.2746997, "idle_time": 8.529816660997312, "inference_duration_total": 0.025394149000931066, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123560.322986, "idle_time": 9.580809574999876, "inference_duration_total": 0.02414380799382343, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123560.3236232, "idle_time": 9.578740204997302, "inference_duration_total": 0.022665778004011372, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123561.3240047, "idle_time": 10.581828026002768, "inference_duration_total": 0.02405155901578837, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123561.3245087, "idle_time": 10.579625515998487, "inference_duration_total": 0.023790215996996267, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123562.3732355, "idle_time": 11.631058941002266, "inference_duration_total": 0.02381393200266757, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123562.373439, "idle_time": 11.628555936000339, "inference_duration_total": 0.02266041699112975, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123563.3750963, "idle_time": 12.630213387998083, "inference_duration_total": 0.023155918992415536, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123563.3753037, "idle_time": 12.633127124001476, "inference_duration_total": 0.025055986996449064, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123564.4231055, "idle_time": 13.680929124002432, "inference_duration_total": 0.025678466008685064, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123564.4237757, "idle_time": 13.678892608000751, "inference_duration_total": 0.024710879017220577, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123565.423914, "idle_time": 14.681737561000773, "inference_duration_total": 0.02599836900481023, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123565.4247181, "idle_time": 14.679834943999595, "inference_duration_total": 0.02513250700212666, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123566.4725266, "idle_time": 15.730350050002016, "inference_duration_total": 0.026219355990178883, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123566.4731166, "idle_time": 15.728233629997703, "inference_duration_total": 0.025595011007681023, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123567.4737604, "idle_time": 16.731583804001275, "inference_duration_total": 0.02428301599866245, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123567.4742417, "idle_time": 16.729358652999508, "inference_duration_total": 0.02277084500019555, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123568.5234094, "idle_time": 17.781233001001965, "inference_duration_total": 0.025016558000061195, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123568.5240479, "idle_time": 17.77916481599823, "inference_duration_total": 0.023462914003175683, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123569.5236802, "idle_time": 18.77879711800051, "inference_duration_total": 0.022649207996437326, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123569.523923, "idle_time": 18.78174653899987, "inference_duration_total": 0.02387600499059772, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123570.5727122, "idle_time": 19.830535776000033, "inference_duration_total": 0.024145684008544777, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123570.5733042, "idle_time": 19.828420904999803, "inference_duration_total": 0.022879773012391524, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123571.5728774, "idle_time": 20.82799413199973, "inference_duration_total": 0.022456677987065632, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123571.5730608, "idle_time": 20.830884482002148, "inference_duration_total": 0.024510479997843504, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123572.622929, "idle_time": 21.880752611999924, "inference_duration_total": 0.024860755998815876, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123572.6238177, "idle_time": 21.878934631997254, "inference_duration_total": 0.023210682997159893, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123573.6227937, "idle_time": 22.87791071799802, "inference_duration_total": 0.026072958997247042, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123573.6229916, "idle_time": 22.880815067001095, "inference_duration_total": 0.028714493975712685, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123574.6733778, "idle_time": 23.928494785999646, "inference_duration_total": 0.025216827001713682, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123574.6735632, "idle_time": 23.931386575000943, "inference_duration_total": 0.030133341995679075, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123575.6729162, "idle_time": 24.930739701001585, "inference_duration_total": 0.03564255899254931, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123575.6730967, "idle_time": 24.928213645998767, "inference_duration_total": 0.03451932799362112, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123576.7233844, "idle_time": 25.981207781002013, "inference_duration_total": 0.027202205015782965, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123576.7235692, "idle_time": 25.97868609699799, "inference_duration_total": 0.024115390009683324, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123577.7227173, "idle_time": 26.980540652002674, "inference_duration_total": 0.02692055701481877, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123577.7232018, "idle_time": 26.978318855999532, "inference_duration_total": 0.02516913401632337, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123578.7236652, "idle_time": 27.98148859900175, "inference_duration_total": 0.024083734995656414, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123578.7241344, "idle_time": 27.97925138299979, "inference_duration_total": 0.023845877993153408, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123579.7730818, "idle_time": 29.028198625997902, "inference_duration_total": 0.025065556008485146, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123579.7732635, "idle_time": 29.031086860002688, "inference_duration_total": 0.02664518500751001, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123580.7737315, "idle_time": 30.031555248002405, "inference_duration_total": 0.0265460869850358, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123580.7741868, "idle_time": 30.029303740000614, "inference_duration_total": 0.026281935985025484, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123581.8234103, "idle_time": 31.081233783999778, "inference_duration_total": 0.024677656994754216, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123581.8239298, "idle_time": 31.07904678900013, "inference_duration_total": 0.02393990300333826, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123582.823191, "idle_time": 32.081014578001486, "inference_duration_total": 0.024546846987504978, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123582.8236973, "idle_time": 32.07881422900027, "inference_duration_total": 0.02397396500236937, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123583.8727057, "idle_time": 33.13052936900203, "inference_duration_total": 0.02537462200052687, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123583.8731985, "idle_time": 33.12831539900071, "inference_duration_total": 0.025070294988836395, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123584.8737178, "idle_time": 34.12883459399745, "inference_duration_total": 0.023219953989610076, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123584.8739855, "idle_time": 34.131809173002694, "inference_duration_total": 0.025263908984925365, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123585.923554, "idle_time": 35.18137775200012, "inference_duration_total": 0.027218836014071712, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123585.9243305, "idle_time": 35.1794474849994, "inference_duration_total": 0.02660533600646886, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123586.9244008, "idle_time": 36.18222439200326, "inference_duration_total": 0.029026531006820733, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123586.9253232, "idle_time": 36.18044019499939, "inference_duration_total": 0.029479446995537728, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123587.975992, "idle_time": 37.23381557600078, "inference_duration_total": 0.0320398599942564, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123587.9762027, "idle_time": 37.23131975399883, "inference_duration_total": 0.03087103200596175, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123588.9736874, "idle_time": 38.23151077900184, "inference_duration_total": 0.03586042999813799, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123588.9741406, "idle_time": 38.22925750899958, "inference_duration_total": 0.027160275996720884, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123590.0232317, "idle_time": 39.28105518099983, "inference_duration_total": 0.03262491099667386, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123590.0234294, "idle_time": 39.27854631899754, "inference_duration_total": 0.033712322012434015, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123591.024001, "idle_time": 40.281824321002205, "inference_duration_total": 0.024451781009702245, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123591.0245607, "idle_time": 40.279677511000045, "inference_duration_total": 0.023717789987131255, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123592.0735002, "idle_time": 41.33132368100269, "inference_duration_total": 0.02538114798880997, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123592.0740888, "idle_time": 41.329205674999685, "inference_duration_total": 0.0235359040016192, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123593.0734131, "idle_time": 42.32853002099728, "inference_duration_total": 0.023098437013686635, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123593.073596, "idle_time": 42.33141937500113, "inference_duration_total": 0.025309522996394662, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123594.1230214, "idle_time": 43.380844879000506, "inference_duration_total": 0.02518712900200626, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123594.1235251, "idle_time": 43.378642126997875, "inference_duration_total": 0.0238228909947793, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123595.1242206, "idle_time": 44.37933756499842, "inference_duration_total": 0.023855699011619436, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123595.1245148, "idle_time": 44.38233848600066, "inference_duration_total": 0.026105382010428002, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123596.1735868, "idle_time": 45.4287039539995, "inference_duration_total": 0.02403697600675514, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123596.1737795, "idle_time": 45.43160293600158, "inference_duration_total": 0.0258219159914006, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123597.172988, "idle_time": 46.43081136800174, "inference_duration_total": 0.025055344001884805, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123597.1735013, "idle_time": 46.4286181949974, "inference_duration_total": 0.024409969999396708, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123598.22315, "idle_time": 47.48097364700152, "inference_duration_total": 0.023334535006142687, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123598.2237883, "idle_time": 47.47890507500051, "inference_duration_total": 0.022814375002781162, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123599.222978, "idle_time": 48.480801596000674, "inference_duration_total": 0.02429439499610453, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123599.2234824, "idle_time": 48.47859939999762, "inference_duration_total": 0.02322058400750393, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123600.2731638, "idle_time": 49.53098740000132, "inference_duration_total": 0.02482487000452238, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123600.273776, "idle_time": 49.52889311999752, "inference_duration_total": 0.02309152997622732, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123601.2730017, "idle_time": 50.528118743997766, "inference_duration_total": 0.023073368003679207, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123601.2731829, "idle_time": 50.531006463002996, "inference_duration_total": 0.02488759099287563, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123602.322671, "idle_time": 51.58049430900064, "inference_duration_total": 0.026669356011552736, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123602.3232913, "idle_time": 51.57840833099908, "inference_duration_total": 0.025055174985027406, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123603.3236358, "idle_time": 52.57875254700048, "inference_duration_total": 0.0293335139976989, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123603.3239045, "idle_time": 52.581728096000006, "inference_duration_total": 0.031073776004632236, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123604.3265731, "idle_time": 53.58439647600244, "inference_duration_total": 0.026422400995215867, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123604.3323007, "idle_time": 53.5874174749988, "inference_duration_total": 0.030791177006904036, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123605.3732157, "idle_time": 54.63103934100218, "inference_duration_total": 0.03138453399515129, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123605.373744, "idle_time": 54.62886094100031, "inference_duration_total": 0.031454507014132105, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123606.3734472, "idle_time": 55.628563972997654, "inference_duration_total": 0.023005058003036538, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123606.3737571, "idle_time": 55.631580734003364, "inference_duration_total": 0.024035763017309364, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123607.4226866, "idle_time": 56.68050997300088, "inference_duration_total": 0.02320565799891483, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123607.4231987, "idle_time": 56.67831555799785, "inference_duration_total": 0.022778304995881626, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123608.4235773, "idle_time": 57.6786941859973, "inference_duration_total": 0.023110205002012663, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123608.4237568, "idle_time": 57.68158026099991, "inference_duration_total": 0.02445264600100927, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123609.4726987, "idle_time": 58.73052207400178, "inference_duration_total": 0.02413925199653022, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123609.4731765, "idle_time": 58.728293502997985, "inference_duration_total": 0.023079997994500445, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123610.4742103, "idle_time": 59.7320339350008, "inference_duration_total": 0.024441732006380334, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123610.475021, "idle_time": 59.730137645998184, "inference_duration_total": 0.023833968003600603, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123611.5227466, "idle_time": 60.78057021300265, "inference_duration_total": 0.025406007993296953, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123611.5233257, "idle_time": 60.778442653998354, "inference_duration_total": 0.023679201993218157, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123612.523388, "idle_time": 61.78121152099993, "inference_duration_total": 0.02693501300382195, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123612.5238428, "idle_time": 61.77895980500034, "inference_duration_total": 0.02382567799577373, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123613.573394, "idle_time": 62.82851104399742, "inference_duration_total": 0.029384760997345438, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123613.5735896, "idle_time": 62.83141299100316, "inference_duration_total": 0.03160654802195495, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123614.5739894, "idle_time": 63.82910627599995, "inference_duration_total": 0.02344427801290294, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123614.5741727, "idle_time": 63.83199620800224, "inference_duration_total": 0.025045917991519673, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123615.623852, "idle_time": 64.88167547300327, "inference_duration_total": 0.02450759699786431, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123615.624033, "idle_time": 64.87914985899988, "inference_duration_total": 0.022853957998449914, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123616.6236787, "idle_time": 65.88150215000132, "inference_duration_total": 0.0246760440095386, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123616.6241715, "idle_time": 65.87928833399928, "inference_duration_total": 0.02286560901120538, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123617.6735547, "idle_time": 66.93137827900136, "inference_duration_total": 0.025245309010642814, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123617.6737392, "idle_time": 66.92885615100022, "inference_duration_total": 0.023927360995003255, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123618.6729765, "idle_time": 67.9307999440025, "inference_duration_total": 0.024571766010922147, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123618.6735065, "idle_time": 67.9286233549974, "inference_duration_total": 0.023169014006271027, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123619.7233908, "idle_time": 68.98121444400022, "inference_duration_total": 0.023914520006655948, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123619.7240057, "idle_time": 68.97912262299724, "inference_duration_total": 0.023088494010153227, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123620.7235007, "idle_time": 69.98132427300152, "inference_duration_total": 0.02593614800571231, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123620.724046, "idle_time": 69.979163005999, "inference_duration_total": 0.024038268009462627, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123621.7736688, "idle_time": 71.03149231800126, "inference_duration_total": 0.023876019989984343, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123621.7742302, "idle_time": 71.02934721799829, "inference_duration_total": 0.022639388003881322, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123622.773002, "idle_time": 72.03082547800295, "inference_duration_total": 0.028490416014392395, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123622.77353, "idle_time": 72.02864690999922, "inference_duration_total": 0.025688629990327172, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123623.8279533, "idle_time": 73.0857769250033, "inference_duration_total": 0.02785154399316525, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123623.828689, "idle_time": 73.08380593599941, "inference_duration_total": 0.024907035007345257, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123624.8230157, "idle_time": 74.08083924300081, "inference_duration_total": 0.025710657999297837, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123624.8235335, "idle_time": 74.07865057099843, "inference_duration_total": 0.02426756200293312, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123625.8731408, "idle_time": 75.13096418200075, "inference_duration_total": 0.023961331993632484, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123625.8733203, "idle_time": 75.12843741599863, "inference_duration_total": 0.02229945600265637, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123626.8728235, "idle_time": 76.1279404779998, "inference_duration_total": 0.0228864920027263, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123626.873009, "idle_time": 76.13083249700139, "inference_duration_total": 0.024466159000439802, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123627.9235034, "idle_time": 77.1813269810009, "inference_duration_total": 0.02364091799245216, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123627.9236865, "idle_time": 77.17880327599778, "inference_duration_total": 0.02260264999131323, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123628.923062, "idle_time": 78.18088563900164, "inference_duration_total": 0.02487632800693973, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123628.9236042, "idle_time": 78.178721166998, "inference_duration_total": 0.02380741900560679, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123629.9236047, "idle_time": 79.18142833900129, "inference_duration_total": 0.023799297003279207, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123629.9240608, "idle_time": 79.17917780999778, "inference_duration_total": 0.02408315099091851, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123630.9734466, "idle_time": 80.23127030800242, "inference_duration_total": 0.026365118013927713, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123630.973632, "idle_time": 80.22874903299817, "inference_duration_total": 0.024904733003495494, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123631.9736032, "idle_time": 81.23142674700284, "inference_duration_total": 0.02514094800062594, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123631.974053, "idle_time": 81.2291696909997, "inference_duration_total": 0.0236052609870967, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123633.0243378, "idle_time": 82.28216123000311, "inference_duration_total": 0.02299170501282788, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123633.0247886, "idle_time": 82.27990570199836, "inference_duration_total": 0.02186844198149629, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123634.0241385, "idle_time": 83.28196213699994, "inference_duration_total": 0.025544170999637572, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123634.0247247, "idle_time": 83.2798416609985, "inference_duration_total": 0.024007781998079736, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123635.0735743, "idle_time": 84.33139797900003, "inference_duration_total": 0.025189261010382324, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123635.0737617, "idle_time": 84.32887878700058, "inference_duration_total": 0.02279215799717349, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123636.0736322, "idle_time": 85.33145581000281, "inference_duration_total": 0.02581612400172162, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123636.074104, "idle_time": 85.32922105699981, "inference_duration_total": 0.023891277000075206, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123637.1230578, "idle_time": 86.38088143300047, "inference_duration_total": 0.035131289001583355, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123637.123691, "idle_time": 86.37880792400028, "inference_duration_total": 0.033388400996045675, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123638.1242976, "idle_time": 87.38212139700045, "inference_duration_total": 0.02379612600270775, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123638.1248415, "idle_time": 87.37995831499939, "inference_duration_total": 0.023146828985773027, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123639.1737127, "idle_time": 88.43153634100236, "inference_duration_total": 0.030064561007748125, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123639.174518, "idle_time": 88.4296349340002, "inference_duration_total": 0.029639561995281838, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123640.173662, "idle_time": 89.4314855440025, "inference_duration_total": 0.026355017002060777, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123640.1741467, "idle_time": 89.42926352399809, "inference_duration_total": 0.02554771500217612, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123641.2232218, "idle_time": 90.48104525900271, "inference_duration_total": 0.028126756002166076, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123641.2237704, "idle_time": 90.47888732799765, "inference_duration_total": 0.02691444400988985, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123642.223993, "idle_time": 91.48181663200012, "inference_duration_total": 0.024069720013358165, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123642.224471, "idle_time": 91.47958797799947, "inference_duration_total": 0.02270988199961721, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123643.2726212, "idle_time": 92.53044470000168, "inference_duration_total": 0.02405331801128341, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123643.2734144, "idle_time": 92.52853133399913, "inference_duration_total": 0.023398928980896017, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123644.2737253, "idle_time": 93.53154885700133, "inference_duration_total": 0.028922811990923947, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123644.2745173, "idle_time": 93.52963423699839, "inference_duration_total": 0.027614328002528055, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123645.3235743, "idle_time": 94.58139814300011, "inference_duration_total": 0.02648742801466142, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123645.324378, "idle_time": 94.57949498199741, "inference_duration_total": 0.025972865008952795, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123646.3240097, "idle_time": 95.5791266089982, "inference_duration_total": 0.027823848999105394, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123646.3242338, "idle_time": 95.58205717400051, "inference_duration_total": 0.029319587993086316, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123647.3728178, "idle_time": 96.63064123300137, "inference_duration_total": 0.027540232993487734, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123647.3736289, "idle_time": 96.62874591599757, "inference_duration_total": 0.027906991006602766, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123648.3727927, "idle_time": 97.63061633899997, "inference_duration_total": 0.02897459001178504, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123648.37332, "idle_time": 97.62843707299908, "inference_duration_total": 0.02828190899163019, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123649.4232903, "idle_time": 98.6811141060025, "inference_duration_total": 0.02652848198704305, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123649.4240572, "idle_time": 98.67917428799774, "inference_duration_total": 0.02603000199451344, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123650.4225688, "idle_time": 99.68039214700184, "inference_duration_total": 0.0218668480119959, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123650.4230566, "idle_time": 99.67817355800071, "inference_duration_total": 0.020808965018659364, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123651.4725657, "idle_time": 100.73038940000333, "inference_duration_total": 0.017997885996010154, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123651.472872, "idle_time": 100.72798887599856, "inference_duration_total": 0.018502447004721034, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123652.4727962, "idle_time": 101.7306197570033, "inference_duration_total": 0.021903685988945654, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123652.4732754, "idle_time": 101.72839225200005, "inference_duration_total": 0.02079905999926268, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123653.5231342, "idle_time": 102.77825119499903, "inference_duration_total": 0.01811358000486507, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123653.5233245, "idle_time": 102.78114798200113, "inference_duration_total": 0.01809596400562441, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123654.5227404, "idle_time": 103.78056400500282, "inference_duration_total": 0.021024167999712517, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123654.5232, "idle_time": 103.77831701700052, "inference_duration_total": 0.02054614001099253, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123655.5237186, "idle_time": 104.77883550799743, "inference_duration_total": 0.019000885000423295, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123655.5239546, "idle_time": 104.78177815900199, "inference_duration_total": 0.01992255800359999, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123656.5734122, "idle_time": 105.83123568300289, "inference_duration_total": 0.01963136499398388, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123656.5735974, "idle_time": 105.8287142679983, "inference_duration_total": 0.018739264000032563, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123657.5732641, "idle_time": 106.83108768100283, "inference_duration_total": 0.01976544000717695, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123657.5738988, "idle_time": 106.829015583, "inference_duration_total": 0.019759759004955413, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123658.6230617, "idle_time": 107.88088541100296, "inference_duration_total": 0.01758539599904907, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123658.6235433, "idle_time": 107.87866026899792, "inference_duration_total": 0.018085237014020095, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123659.6232858, "idle_time": 108.8811094010016, "inference_duration_total": 0.01987727399682626, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123659.6237884, "idle_time": 108.87890530299774, "inference_duration_total": 0.019131509008730063, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123660.6723337, "idle_time": 109.93015737700262, "inference_duration_total": 0.019495368007483194, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123660.6727738, "idle_time": 109.92789077499765, "inference_duration_total": 0.0190010569858714, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123661.6737792, "idle_time": 110.9288961889979, "inference_duration_total": 0.02047186599156703, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123661.6740649, "idle_time": 110.93188851000014, "inference_duration_total": 0.021375169988459675, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123662.7221854, "idle_time": 111.98000900400075, "inference_duration_total": 0.018095905008522095, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123662.7229068, "idle_time": 111.97802378899723, "inference_duration_total": 0.018831030993169406, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123663.723517, "idle_time": 112.98134042099991, "inference_duration_total": 0.01848820301165688, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123663.7240026, "idle_time": 112.9791193739984, "inference_duration_total": 0.018622983996465337, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123664.7728994, "idle_time": 114.03072297400286, "inference_duration_total": 0.01988839999467018, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123664.7735186, "idle_time": 114.0286356079996, "inference_duration_total": 0.019582053013436962, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123665.772721, "idle_time": 115.03054479700222, "inference_duration_total": 0.01875487701181555, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123665.77321, "idle_time": 115.02832696899713, "inference_duration_total": 0.0186674000142375, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123666.8227072, "idle_time": 116.08053065200147, "inference_duration_total": 0.02084787600688287, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123666.8232782, "idle_time": 116.07839509399855, "inference_duration_total": 0.020315602010668954, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123667.8242302, "idle_time": 117.07934715800002, "inference_duration_total": 0.019155915008013835, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123667.8247461, "idle_time": 117.08256979300131, "inference_duration_total": 0.020635400996980025, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123668.8724568, "idle_time": 118.13028028100234, "inference_duration_total": 0.020584815021720715, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123668.8731, "idle_time": 118.12821683599759, "inference_duration_total": 0.020573563015204854, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123669.873486, "idle_time": 119.13130971999999, "inference_duration_total": 0.018917697005235823, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123669.8739736, "idle_time": 119.12909040899831, "inference_duration_total": 0.018613049014675198, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123670.922144, "idle_time": 120.17996768600278, "inference_duration_total": 0.0200333440079703, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123670.9227612, "idle_time": 120.17787819399746, "inference_duration_total": 0.02004431599198142, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123671.9231281, "idle_time": 121.18095164199985, "inference_duration_total": 0.018086500011122553, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123671.923604, "idle_time": 121.17872100199747, "inference_duration_total": 0.018319616006920114, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123672.973462, "idle_time": 122.23128559900215, "inference_duration_total": 0.018510269990656525, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123672.9736407, "idle_time": 122.22875766300058, "inference_duration_total": 0.018094463001034455, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123673.9720159, "idle_time": 123.22983941200073, "inference_duration_total": 0.018774712982121855, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123673.9725134, "idle_time": 123.22763054799725, "inference_duration_total": 0.018912620002083713, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123675.0221488, "idle_time": 124.27997243700156, "inference_duration_total": 0.01704614701884566, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123675.0227525, "idle_time": 124.27786955400006, "inference_duration_total": 0.01719733299978543, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123676.0229206, "idle_time": 125.28074418600227, "inference_duration_total": 0.019558385993150296, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123676.0231721, "idle_time": 125.27828901999965, "inference_duration_total": 0.01922074300091481, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123677.0729473, "idle_time": 126.33077076600239, "inference_duration_total": 0.01693260099636973, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123677.0736635, "idle_time": 126.32878037699993, "inference_duration_total": 0.017817934007325675, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123678.072934, "idle_time": 127.32805093999923, "inference_duration_total": 0.019164780002029147, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123678.0731227, "idle_time": 127.33094638200055, "inference_duration_total": 0.019870464006089605, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123679.123191, "idle_time": 128.37830799199946, "inference_duration_total": 0.02007489700554288, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123679.1233902, "idle_time": 128.38121366700216, "inference_duration_total": 0.02092199200342293, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123680.1226704, "idle_time": 129.38049395200142, "inference_duration_total": 0.020555814993713284, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123680.1229289, "idle_time": 129.37804571499873, "inference_duration_total": 0.02005803100473713, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123681.1228023, "idle_time": 130.3806256650023, "inference_duration_total": 0.017706957023619907, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123681.123275, "idle_time": 130.37839205599812, "inference_duration_total": 0.018209540994575946, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123682.1727002, "idle_time": 131.43052368100325, "inference_duration_total": 0.01670804999594111, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123682.1732345, "idle_time": 131.42835145799836, "inference_duration_total": 0.017546565006341552, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123683.173113, "idle_time": 132.43093670800226, "inference_duration_total": 0.0212233149795793, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123683.1736093, "idle_time": 132.42872615799934, "inference_duration_total": 0.02067808500942192, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123684.2224789, "idle_time": 133.48030237000057, "inference_duration_total": 0.020978450986149255, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123684.2228954, "idle_time": 133.47801238099782, "inference_duration_total": 0.021149860993318725, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123685.223428, "idle_time": 134.4812515880003, "inference_duration_total": 0.022184900000866037, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123685.2238832, "idle_time": 134.47900028299773, "inference_duration_total": 0.02090905400837073, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123686.2729418, "idle_time": 135.53076546100056, "inference_duration_total": 0.02043303399477736, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123686.273432, "idle_time": 135.5285490589995, "inference_duration_total": 0.01951216997622396, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123687.2727609, "idle_time": 136.53058448500087, "inference_duration_total": 0.02137134299846366, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123687.2732072, "idle_time": 136.52832430499984, "inference_duration_total": 0.020464531997276936, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123688.3221788, "idle_time": 137.58000234000065, "inference_duration_total": 0.01751871800297522, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123688.3227801, "idle_time": 137.57789714499813, "inference_duration_total": 0.017302166004810715, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123689.3236692, "idle_time": 138.58149285200125, "inference_duration_total": 0.017794298018998234, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123689.3241205, "idle_time": 138.57923757999743, "inference_duration_total": 0.01761935900140088, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123690.3720565, "idle_time": 139.62988028600012, "inference_duration_total": 0.018292050019226735, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123690.3728259, "idle_time": 139.62794281199967, "inference_duration_total": 0.018752264004433528, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123691.373441, "idle_time": 140.6312644910031, "inference_duration_total": 0.017740540984959807, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123691.3738837, "idle_time": 140.62900051500037, "inference_duration_total": 0.017680883003777126, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123692.4222093, "idle_time": 141.68003268500252, "inference_duration_total": 0.01855398299812805, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123692.4227889, "idle_time": 141.67790580399742, "inference_duration_total": 0.01844939498914755, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123693.4226782, "idle_time": 142.68050193800082, "inference_duration_total": 0.017131617001723498, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123693.4231455, "idle_time": 142.67826242199953, "inference_duration_total": 0.01700841601996217, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123694.472947, "idle_time": 143.7307706060019, "inference_duration_total": 0.018979990014486248, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123694.4731226, "idle_time": 143.72823949699887, "inference_duration_total": 0.018141202996048378, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123695.4728696, "idle_time": 144.73069321300136, "inference_duration_total": 0.017563822002557572, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123695.4733016, "idle_time": 144.7284186020006, "inference_duration_total": 0.017103713002143195, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123696.522287, "idle_time": 145.78011024400257, "inference_duration_total": 0.01767674700022326, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123696.5229566, "idle_time": 145.77807351599768, "inference_duration_total": 0.017909825015522074, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123697.5230958, "idle_time": 146.78091930800292, "inference_duration_total": 0.018158247006795136, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123697.5236347, "idle_time": 146.77875164500074, "inference_duration_total": 0.018693021000217414, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123698.5728967, "idle_time": 147.82801365299747, "inference_duration_total": 0.01891862200864125, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123698.573076, "idle_time": 147.830899468001, "inference_duration_total": 0.019806812018941855, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123699.572565, "idle_time": 148.83038845800183, "inference_duration_total": 0.01829506101057632, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123699.5730631, "idle_time": 148.82818005999798, "inference_duration_total": 0.017808297008741647, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123700.6230955, "idle_time": 149.8809190050015, "inference_duration_total": 0.017468425998231396, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123700.623571, "idle_time": 149.87868779999917, "inference_duration_total": 0.018193417006841628, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123701.6227648, "idle_time": 150.8805884170033, "inference_duration_total": 0.01884947399958037, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123701.6233935, "idle_time": 150.8785105629977, "inference_duration_total": 0.018151194002712145, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123702.6727622, "idle_time": 151.93058551700233, "inference_duration_total": 0.01608490600483492, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123702.6733146, "idle_time": 151.92843150000044, "inference_duration_total": 0.016946146999544, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123703.6726956, "idle_time": 152.93051918700075, "inference_duration_total": 0.019478818991046865, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123703.6731634, "idle_time": 152.92828050800017, "inference_duration_total": 0.019208098001399776, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123704.7221472, "idle_time": 153.97997081300127, "inference_duration_total": 0.01950310000029276, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123704.7226837, "idle_time": 153.97780050599977, "inference_duration_total": 0.019375124993530335, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123705.7233117, "idle_time": 154.97842856699936, "inference_duration_total": 0.019165329009410925, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123705.7235265, "idle_time": 154.98135012400235, "inference_duration_total": 0.0195495920124813, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123706.722852, "idle_time": 155.98067554700174, "inference_duration_total": 0.019315316996653564, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123706.7233076, "idle_time": 155.97842452499754, "inference_duration_total": 0.019489667985908454, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123707.7721512, "idle_time": 157.02997477300232, "inference_duration_total": 0.01668954001434031, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123707.7725935, "idle_time": 157.0277104559973, "inference_duration_total": 0.017300971998338355, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123708.7725875, "idle_time": 158.03041115100132, "inference_duration_total": 0.018912888004706474, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123708.7730212, "idle_time": 158.02813797699855, "inference_duration_total": 0.018734660996415187, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123709.8224008, "idle_time": 159.07751768899834, "inference_duration_total": 0.017250100001547253, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123709.8225703, "idle_time": 159.08039364400247, "inference_duration_total": 0.016943532991717802, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123710.8232505, "idle_time": 160.07836730299823, "inference_duration_total": 0.020016650003526593, "inference_count": 32, "speech_id": null, "error": null}, {"type": "vad_metrics", "timestamp": 1754123710.8234541, "idle_time": 160.08127782700103, "inference_duration_total": 0.020577980001689866, "inference_count": 32, "speech_id": null, "error": null}], "conversation_history": [{"timestamp": "2025-08-02T08:32:30.742306", "role": "system", "content": "Greet the user warmly and ask them how you can help them today about p2p lending."}, {"timestamp": "2025-08-02T08:32:41.016474", "role": "assistant", "content": "Namaste! Main <PERSON><PERSON><PERSON> hoon, aapka P to P lending advocate at लेन-देन Club. Aapko P to P lending ke baare mein kaise madad kar sakti hoon aaj?"}, {"timestamp": "2025-08-02T08:32:44.166225", "role": "user", "content": "how are u"}, {"timestamp": "2025-08-02T08:32:52.786829", "role": "assistant", "content": "Main theek hoon, shu<PERSON><PERSON>! Aap kaise ho? Aapko P to P lending ya लेन-देन Club ke baare mein kuch puchna hai?"}, {"timestamp": "2025-08-02T08:32:54.931531", "role": "user", "content": "who are the founders"}, {"timestamp": "2025-08-02T08:33:02.871239", "role": "user", "content": "what is manual lending"}, {"timestamp": "2025-08-02T08:33:02.872345", "role": "assistant", "content": "लेन-देन Club ke founders <PERSON><PERSON><PERSON> aur <PERSON>,"}, {"timestamp": "2025-08-02T08:33:21.245883", "role": "user", "content": "ok wyd"}, {"timestamp": "2025-08-02T08:33:21.246708", "role": "assistant", "content": "Founders of लेन-देन Club ha<PERSON> aur <PERSON>, jo 2015 mein is platform ko shuru kiya tha.\n\nAur manual lending ka matlab hota hai ki aap khud har borrower ko chun sakti hain, starting from just two hundred fifty rupees per loan. Is tarah aapke paas poora control"}, {"timestamp": "2025-08-02T08:33:30.347289", "role": "user", "content": "fine"}, {"timestamp": "2025-08-02T08:33:30.348219", "role": "assistant", "content": "<PERSON><PERSON><PERSON> sawal ka matlab hai \"manual lending\"? Manual lending ka matlab hota hai jab lender khud apne decisions lekar, bina automated system ke,"}, {"timestamp": "2025-08-02T08:33:41.928331", "role": "user", "content": "ok"}, {"timestamp": "2025-08-02T08:33:41.929151", "role": "assistant", "content": "Achha hai! Agar aap apne paise ko fixed deposits ya mutual funds se zyada accha return dena chahte hain, toh P to P lending ek bahut hi accha option hai. Aapke financial"}, {"timestamp": "2025-08-02T08:33:49.489370", "role": "assistant", "content": "Aapko P to P lending ke baare mein aur jaankari chahiye toh main yahan hoon madad ke liye. Ka<PERSON>hi bhi puchna!"}, {"timestamp": "2025-08-02T08:33:50.916499", "role": "user", "content": "no, who is dipesh"}, {"timestamp": "2025-08-02T08:34:03.611795", "role": "user", "content": "ok fine no need"}, {"timestamp": "2025-08-02T08:34:03.612623", "role": "assistant", "content": "Dipesh <PERSON> लेन-देन Club ke co-founder hain, jinke paas Electronics aur Communication Engineering mein BTech degree hai NIT Kurukshetra se. Unhone"}, {"timestamp": "2025-08-02T08:34:12.212884", "role": "assistant", "content": "<PERSON><PERSON> hai, jab bhi a<PERSON>ko P to P lending ya लेन-देन Club ke baare mein baat karni ho, main yahin hoon. Aapka din achha rahe!"}, {"timestamp": "2025-08-02T08:34:47.815312", "role": "assistant", "content": "Kya aap abhi bhi yahan hain? Agar aapko koi madad chahiye toh bataiye."}, {"timestamp": "2025-08-02T08:35:10.353585", "role": "assistant", "content": "Kya aap abhi bhi hain? <PERSON><PERSON> a<PERSON>ko <PERSON> to P lending ya लेन-देन Club ke baare mein kuch puchna ho toh bataiyega."}], "function_calls": [{"timestamp": "2025-08-02T08:32:57.243544", "function_name": "search_knowledge_base", "arguments": {"query": "founders of लेन-देन Club"}, "result": "1. 📌 Question: Who are the founders of LenDenClub?\n💡 Answer: The founders of LenDenClub are <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.\n\n2. 📌 Question: Who founded LenDenClub and in which year?\n💡 Answer: LenDenClu..."}, {"timestamp": "2025-08-02T08:33:05.388407", "function_name": "search_knowledge_base", "arguments": {"query": "manual lending in P to P lending"}, "result": "1. 📌 Question: What is manual lending?\n💡 Answer: In Manual lending - you pick each individual borrower yourself. You can start with just 250 rupees per loan per borrower and choose based on their prof..."}, {"timestamp": "2025-08-02T08:33:05.437415", "function_name": "search_knowledge_base", "arguments": {"query": "founders of लेन-देन Club"}, "result": "1. 📌 Question: Who are the founders of LenDenClub?\n💡 Answer: The founders of LenDenClub are <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.\n\n2. 📌 Question: Who founded LenDenClub and in which year?\n💡 Answer: LenDenClu..."}, {"timestamp": "2025-08-02T08:33:53.012996", "function_name": "search_knowledge_base", "arguments": {"query": "Dipesh Karki"}, "result": "1. 📌 Question: What prior experience does <PERSON><PERSON><PERSON> have?\n💡 Answer: <PERSON><PERSON><PERSON> previously led software development at IDA Business Solutions and Newgen Software, underpinned by a B.Tech in ECE from NIT..."}], "usage_summary": "UsageSummary(llm_prompt_tokens=22007, llm_prompt_cached_tokens=12928, llm_completion_tokens=735, tts_characters_count=2140, tts_audio_duration=122.58066666666656, stt_audio_duration=0.0)"}