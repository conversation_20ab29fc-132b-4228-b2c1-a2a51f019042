"""
Neura Voice P2P Lending Agent
This module provides a voice-enabled agent for P2P lending queries using
LlamaIndex for RAG (Retrieval-Augmented Generation) and vector search.
"""

# Standard library imports
import asyncio
import logging
import os
import sys
import time
from typing import Annotated, Optional
from datetime import datetime

# Third-party imports
from dotenv import load_dotenv
import json
import requests

# Local imports
from prompt import get_enhanced_system_prompt
from linkup import LinkupClient
from clients.retrive import VectorDBLoader

# LiveKit imports
from livekit.agents import (
    AgentSession,
    JobContext,
    WorkerOptions,
    cli,
    UserStateChangedEvent,
    metrics,
    MetricsCollectedEvent,
    JobProcess,
    AudioConfig,
    BackgroundAudioPlayer,
    BuiltinAudioClip,
    vad,
)
from livekit.agents.voice import Agent
from livekit.plugins import openai, noise_cancellation, google, silero
from livekit.agents.llm import ChatContext, function_tool
from livekit.agents.voice.room_io import RoomInputOptions, RoomOutputOptions
from livekit.agents.metrics import LLMMetrics, STTMetrics, TTSMetrics, EOUMetrics

# Load environment variables
load_dotenv()

# Constants
LOGS_DIR = "logs"
METRICS_DIR = "metrics"
PERSIST_DIR = "./neura-voice-p2p-engine-storage"
DOCS_DIR = "docs"
QUESTION_COL = "question"
ANSWER_COL = "answer"
BATCH_SIZE = 100
MEMORY_WARN_MB = 1024

# Ensure directories exist
os.makedirs(LOGS_DIR, exist_ok=True)
os.makedirs(METRICS_DIR, exist_ok=True)

# Custom log filter to reduce noise
class ImportantLogsFilter(logging.Filter):
    def filter(self, record):
        if record.levelno >= logging.WARNING:
            return True
        if record.name == '__main__' or 'main_agent' in record.name:
            return True
        suppressed_loggers = [
            'httpcore', 'httpx', 'rustls', 'tungstenite', 'livekit_api',
            'openai._base_client', 'anyio', 'urllib3'
        ]
        if any(name in record.name for name in suppressed_loggers):
            return False
        if 'Added VAD metrics' in record.getMessage():
            if not hasattr(self, '_vad_counter'):
                self._vad_counter = 0
            self._vad_counter += 1
            return self._vad_counter % 10 == 0
        if 'json_data' in record.getMessage() and len(record.getMessage()) > 1000:
            return False
        return True

# Global variables
log_file_path = None
_vector_db_loader: Optional[VectorDBLoader] = None
_metrics_collector = None

def _extract_text_content(content):
    """Extract plain text from content (str, list, or dict)."""
    if isinstance(content, str):
        return content.strip()
    elif isinstance(content, list):
        return "".join(
            str(part.get("text", part)) if isinstance(part, dict) else str(part)
            for part in content
        ).strip()
    return str(content).strip()

class SessionMetricsCollector:
    """Collects and manages metrics for a single session with speech_id linking."""
    def __init__(self, session_name: str):
        self.session_name = session_name
        self.session_start_time = datetime.now()
        self.metrics = {
            "session_info": {
                "session_name": session_name,
                "start_time": self.session_start_time.isoformat(),
                "end_time": None,
                "duration_seconds": None
            },
            "structured_conversation": [],
            "usage_summary": None
        }
        # Buffers for metrics before speech_id is known
        self._pending_metrics = {
            "stt": {},
            "eou": {},
            "llm": [],
            "tts": {}
        }
        self._pending_function_calls = []

    def _get_iso_timestamp(self, ts) -> Optional[str]:
        if isinstance(ts, datetime):
            return ts.isoformat()
        if isinstance(ts, (int, float)):
            return datetime.fromtimestamp(ts).isoformat()
        return None

    def _get_turn_by_speech_id(self, speech_id: str, role: str):
        """Find turn by speech_id in current or past turns."""
        for turn in reversed(self.metrics["structured_conversation"]):
            if turn is None:
                continue
            if role == "user":
                user_turn = turn.get("user_turn")
                if user_turn and user_turn.get("speech_id") == speech_id:
                    return turn
            elif role == "assistant":
                assistant_turn = turn.get("assistant_turn")
                if assistant_turn and assistant_turn.get("speech_id") == speech_id:
                    return turn
        return None

    def add_stt_metrics(self, metrics: STTMetrics):
        # STTMetrics doesn't have speech_id field in base.py, so we can't link by speech_id
        # Store in pending and associate with the most recent user turn
        metrics_data = {
            "type": metrics.type,
            "label": metrics.label,
            "request_id": metrics.request_id,
            "timestamp": self._get_iso_timestamp(metrics.timestamp),
            "duration": metrics.duration,
            "audio_duration": metrics.audio_duration,
            "streamed": metrics.streamed
        }

        # Find the most recent user turn without STT metrics
        for turn in reversed(self.metrics["structured_conversation"]):
            if turn and turn.get("user_turn") and "metrics" in turn["user_turn"]:
                if not turn["user_turn"]["metrics"]["stt"]:  # No STT metrics yet
                    turn["user_turn"]["metrics"]["stt"].append(metrics_data)
                    return
        
        # If no suitable turn found, store in a generic pending list
        if "general" not in self._pending_metrics["stt"]:
            self._pending_metrics["stt"]["general"] = []
        self._pending_metrics["stt"]["general"].append(metrics_data)



    def add_eou_metrics(self, metrics: EOUMetrics):
        speech_id = getattr(metrics, 'speech_id', None)
        if not speech_id:
            return

        metrics_data = {
            "type": metrics.type,
            "timestamp": self._get_iso_timestamp(metrics.timestamp),
            "end_of_utterance_delay": metrics.end_of_utterance_delay,
            "transcription_delay": metrics.transcription_delay,
            "on_user_turn_completed_delay": metrics.on_user_turn_completed_delay,
            "last_speaking_time": metrics.last_speaking_time,
            "speech_id": speech_id
        }

        # Try to find the corresponding user turn by speech_id first
        turn = self._get_turn_by_speech_id(speech_id, "user")
        if turn and turn.get("user_turn") and "metrics" in turn["user_turn"]:
            turn["user_turn"]["metrics"]["eou"].append(metrics_data)
            return
        
        # If not found, try to find the most recent user turn without EOU metrics
        for turn in reversed(self.metrics["structured_conversation"]):
            if (turn and turn.get("user_turn") and "metrics" in turn["user_turn"] 
                and not turn["user_turn"]["metrics"]["eou"]):  # No EOU metrics yet
                turn["user_turn"]["metrics"]["eou"].append(metrics_data)
                return
        
        # Store in pending if no suitable turn found
        if speech_id not in self._pending_metrics["eou"]:
            self._pending_metrics["eou"][speech_id] = []
        self._pending_metrics["eou"][speech_id].append(metrics_data)

    def add_llm_metrics(self, metrics: LLMMetrics):
        if metrics.cancelled:
            return

        metrics_data = {
            "type": metrics.type,
            "label": metrics.label,
            "request_id": metrics.request_id,
            "timestamp": self._get_iso_timestamp(metrics.timestamp),
            "duration": metrics.duration,
            "ttft": metrics.ttft,
            "cancelled": metrics.cancelled,
            "completion_tokens": metrics.completion_tokens,
            "prompt_tokens": metrics.prompt_tokens,
            "prompt_cached_tokens": metrics.prompt_cached_tokens,
            "total_tokens": metrics.total_tokens,
            "tokens_per_second": metrics.tokens_per_second,
            "speech_id": getattr(metrics, 'speech_id', None)
        }
        self._pending_metrics["llm"].append(metrics_data)

    def add_tts_metrics(self, metrics: TTSMetrics):
        if metrics.cancelled:
            return

        speech_id = getattr(metrics, 'speech_id', None)
        
        metrics_data = {
            "type": metrics.type,
            "label": metrics.label,
            "request_id": metrics.request_id,
            "timestamp": self._get_iso_timestamp(metrics.timestamp),
            "ttfb": metrics.ttfb,
            "duration": metrics.duration,
            "audio_duration": metrics.audio_duration,
            "cancelled": metrics.cancelled,
            "characters_count": metrics.characters_count,
            "streamed": metrics.streamed,
            "segment_id": getattr(metrics, 'segment_id', None),
            "speech_id": speech_id
        }

        # Try to find the corresponding assistant turn by speech_id
        if speech_id:
            turn = self._get_turn_by_speech_id(speech_id, "assistant")
            if turn and turn.get("assistant_turn") and "metrics" in turn["assistant_turn"]:
                turn["assistant_turn"]["metrics"]["tts"].append(metrics_data)
                return
        
        # If no speech_id or turn found, find the most recent assistant turn without TTS metrics
        for turn in reversed(self.metrics["structured_conversation"]):
            if (turn and turn.get("assistant_turn") and "metrics" in turn["assistant_turn"] 
                and not turn["assistant_turn"]["metrics"]["tts"]):  # No TTS metrics yet
                turn["assistant_turn"]["metrics"]["tts"].append(metrics_data)
                return
        
        # Store in pending if no suitable turn found
        if speech_id:
            if speech_id not in self._pending_metrics["tts"]:
                self._pending_metrics["tts"][speech_id] = []
            self._pending_metrics["tts"][speech_id].append(metrics_data)
        else:
            # Store in general pending for association later
            if "general" not in self._pending_metrics["tts"]:
                self._pending_metrics["tts"]["general"] = []
            self._pending_metrics["tts"]["general"].append(metrics_data)

    def add_conversation_entry(self, role: str, content: str, speech_id: str = None):
        if role == "user":
            # Get pending STT and EOU metrics (they may be stored under speech_id or "general")
            stt_metrics = []
            eou_metrics = []
            
            if speech_id:
                stt_metrics = self._pending_metrics["stt"].pop(speech_id, [])
                eou_metrics = self._pending_metrics["eou"].pop(speech_id, [])
            
            # Also check for general metrics (for STT that don't have speech_id)
            if "general" in self._pending_metrics["stt"]:
                stt_metrics.extend(self._pending_metrics["stt"].pop("general", []))
            
            user_turn = {
                "timestamp": datetime.now().isoformat(),
                "content": content,
                "speech_id": speech_id,
                "metrics": {
                    "stt": stt_metrics,
                    "eou": eou_metrics,
                }
            }
            current_turn = {"user_turn": user_turn, "assistant_turn": None}
            if current_turn is not None:  # Safety check
                self.metrics["structured_conversation"].append(current_turn)

        elif role == "assistant":
            current_turn = next(
                (t for t in reversed(self.metrics["structured_conversation"]) if t is not None and t.get("assistant_turn") is None),
                None
            )
            if not current_turn:
                current_turn = {"assistant_turn": None}
                self.metrics["structured_conversation"].append(current_turn)

            # Get pending TTS metrics (they may be stored under speech_id or "general")
            tts_metrics = []
            if speech_id:
                tts_metrics = self._pending_metrics["tts"].pop(speech_id, [])
            
            # Also check for general TTS metrics
            if "general" in self._pending_metrics["tts"]:
                tts_metrics.extend(self._pending_metrics["tts"].pop("general", []))

            assistant_turn = {
                "timestamp": datetime.now().isoformat(),
                "content": content,
                "speech_id": speech_id,
                "function_calls": self._pending_function_calls.copy(),
                "metrics": {
                    "llm": self._pending_metrics["llm"].copy(),
                    "tts": tts_metrics
                }
            }
            if current_turn is not None:  # Safety check
                current_turn["assistant_turn"] = assistant_turn
            self._pending_metrics["llm"].clear()
            self._pending_function_calls.clear()

    def add_function_call(self, function_name: str, args: dict, result: str = None):
        entry = {
            "timestamp": datetime.now().isoformat(),
            "function_name": function_name,
            "arguments": args,
            "result": result
        }
        self._pending_function_calls.append(entry)

    def set_usage_summary(self, summary: str):
        self.metrics["usage_summary"] = summary

    def finalize_session(self):
        end_time = datetime.now()
        self.metrics["session_info"]["end_time"] = end_time.isoformat()
        self.metrics["session_info"]["duration_seconds"] = (end_time - self.session_start_time).total_seconds()

        # Clean up speech_id from output (but keep in metrics for tracking)
        for turn in self.metrics["structured_conversation"]:
            if turn is None:
                continue
            if "user_turn" in turn and turn["user_turn"]:
                turn["user_turn"].pop("speech_id", None)
            if "assistant_turn" in turn and turn["assistant_turn"]:
                turn["assistant_turn"].pop("speech_id", None)

    def save_to_json(self):
        try:
            self.finalize_session()
            filename = f"{self.session_name}_metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            filepath = os.path.join(METRICS_DIR, filename)
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.metrics, f, indent=2, ensure_ascii=False)
            logging.getLogger(__name__).info(f"✅ Session metrics saved to: {filepath}")
        except Exception as e:
            logging.getLogger(__name__).error(f"❌ Failed to save metrics: {e}")


async def get_vector_db_loader() -> VectorDBLoader:
    global _vector_db_loader
    if _vector_db_loader is None:
        loop = asyncio.get_running_loop()
        _vector_db_loader = await loop.run_in_executor(None, VectorDBLoader)
    return _vector_db_loader


@function_tool
async def search_knowledge_base(
    query: Annotated[
        str,
        """Convert current user's query into a comprehensive search query,
        optimized for vector similarity matching.
        """
    ]
) -> str:
    """
    Searches the internal knowledge base for accurate answers about P2P lending, 
    Lenden club, investment products, services, founders, and other related topics.
    This is the **primary and only** source for responding to user queries on these subjects.  
    Use this tool **only for directly relevant questions**, and refer to **conversation history** to 
    avoid redundant lookups of previously fetched information.
    Args:
        query (str): A user question converted into a search-optimized query string.
    Returns:
        str: Formatted Q&A pairs retrieved from the knowledge base.
    """
    global _metrics_collector
    loader = await get_vector_db_loader()
    results = await loader.search(query, top_k=2)
    if not results:
        result = "I'm sorry, I couldn't find relevant information in the knowledge base."
    else:
        qa_texts = [f"{idx}. {item.get('formatted_text', '').strip()}" for idx, item in enumerate(results, start=1)]
        result = "\n".join(qa_texts)

    if _metrics_collector:
        _metrics_collector.add_function_call("search_knowledge_base", {"query": query}, result)
    return result


@function_tool
async def web_search(
    query: Annotated[
        str,
        "Make query as specific as possible with additional context. "
        "Transform 'what's the weather' to 'what is the weather in [city] "
        "today'. Add relevant constraints, dates, or location details for "
        "better results."
    ]
) -> str:
    """
    Search web for real-time information.
    Search for current events, news, weather, stock prices, or recent data 
    not in knowledge base. Called when user asks to search web, google 
    something, or requests current/live information.
    Args:
        query: The web search query string.
    Returns:
        str: The search result or error message.
    """
    global _metrics_collector
    try:
        api_key = os.getenv('LINKUP_API_KEY')
        client = LinkupClient(api_key=api_key)
        def do_search():
            return client.search(
                query=query,
                depth="standard",
                output_type="sourcedAnswer",
            )
        loop = asyncio.get_running_loop()
        response = await loop.run_in_executor(None, do_search)
        result = response.answer if hasattr(response, 'answer') and response.answer else "No direct answer found."
    except Exception as e:
        logging.getLogger(__name__).error(f"Web search error: {e}")
        result = "Web search error. Please try again later."

    if _metrics_collector:
        _metrics_collector.add_function_call("web_search", {"query": query}, result)
    return result


# Global flag to prevent multiple report requests
_report_requested_global = False

@function_tool
async def get_report_with_verification(
    last_four_digits: Annotated[
        str,
        "The last 4 digits of the user's registered mobile number for verification."
    ]
) -> str:
    """
    ONLY call this function when the user explicitly asks for their report, statement, or account statement.
    This generates and emails the user's FMPP account statement for the last 12 months.
    Requires verification of last 4 digits of registered mobile number, ask the user for this before calling the function.  
    """
    global _report_requested_global
    registered_numbers = ["**********"]

    def convert_hindi_to_english(text):
        hindi_to_english = {'०': '0', '१': '1', '२': '2', '३': '3', '४': '4', '५': '5', '६': '6', '७': '7', '८': '8', '९': '9'}
        for h, e in hindi_to_english.items():
            text = text.replace(h, e)
        return text

    def extract_digits(text):
        text = convert_hindi_to_english(text)
        return ''.join(filter(str.isdigit, text))

    try:
        input_digits = extract_digits(last_four_digits)
        if len(input_digits) < 4:
            return "Please provide the complete four digits of your registered mobile number."
        user_last_four = input_digits[-4:]

        matched_number = next((num for num in registered_numbers if num.endswith(user_last_four)), None)
        if not matched_number:
            return f"The last 4 digits you provided ({user_last_four}) do not match our records."

        if _report_requested_global:
            return "Your statement for this session has already been generated. You can only request one statement per session."

        _report_requested_global = True
        contact_number = matched_number

        # API calls (your existing logic)
        url = "https://qa-investor-api.lendenclub.com/api/ims/common/v1/get-user-token?mobile_number="
        response = requests.get(url + contact_number)
        if response.status_code != 200:
            _report_requested_global = False
            return f"Failed to generate report for {contact_number}. Please try again or contact support."

        token_info = response.json().get('data', {})
        access_token = token_info.get('access_token')
        ldc_code = token_info.get('ldc_code')
        if not access_token or not ldc_code:
            _report_requested_global = False
            return "There was an issue verifying your account details."

        headers = {
            "Authorization": f"Token {access_token}",
            "x-ldc-key": ldc_code,
            "User-Agent": "Mozilla/5.0",
            "Content-Type": "application/json"
        }
        params = {
            "statement_type": "FMPP_ACCOUNT_STATEMENT",
            "months": "12",
            "send_email": "true"
        }
        report_url = "https://qa-investor-api.lendenclub.com/api/ims/retail-investor/v5/web/statement-type"
        email_response = requests.get(report_url, headers=headers, params=params)

        if email_response.status_code == 200:
            return f"✅ आपकी 12-month FMPP account statement successfully generate हो गई है और आपके registered email address पर send कर दी गई है। कृपया अपना email check करें। Contact number: {contact_number}"
        else:
            _report_requested_global = False
            return f"Report generation failed. Response code: {email_response.status_code}. Please try later."

    except requests.RequestException as e:
        _report_requested_global = False
        return "Network error. Please check your connection and try again."
    except Exception as e:
        _report_requested_global = False
        return "Technical error occurred. Please contact customer support."


class NeuraVoiceAgent(Agent):
    def __init__(self, metrics_collector: SessionMetricsCollector) -> None:
        stt = openai.STT(model='gpt-4o-mini-transcribe')
        llm = openai.LLM(model='gpt-4o-mini', temperature=0.6)
        tts = google.TTS(
            language='hi-IN',
            gender='female',
            voice_name='hi-IN-Chirp3-HD-Laomedeia',
            sample_rate=24000,
            speaking_rate=0.95,
            use_streaming=True,
            location='asia-southeast1',
            credentials_file=os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
        )
        silero_vad = silero.VAD.load()

        super().__init__(
            instructions=get_enhanced_system_prompt(),
            stt=stt,
            llm=llm,
            tts=tts,
            vad=silero_vad,
            tools=[
                search_knowledge_base,
                web_search,
                get_report_with_verification
            ]
        )
        self.metrics_collector = metrics_collector

        # Set up metric handlers
        llm.on("metrics_collected", lambda m: self.on_llm_metrics_collected(m))
        stt.on("metrics_collected", lambda m: self.on_stt_metrics_collected(m))
        stt.on("eou_metrics_collected", lambda m: self.on_eou_metrics_collected(m))
        tts.on("metrics_collected", lambda m: self.on_tts_metrics_collected(m))


    def on_llm_metrics_collected(self, metrics: LLMMetrics):
        self.metrics_collector.add_llm_metrics(metrics)

    def on_stt_metrics_collected(self, metrics: STTMetrics):
        self.metrics_collector.add_stt_metrics(metrics)

    def on_eou_metrics_collected(self, metrics: EOUMetrics):
        self.metrics_collector.add_eou_metrics(metrics)

    def on_tts_metrics_collected(self, metrics: TTSMetrics):
        self.metrics_collector.add_tts_metrics(metrics)



    async def on_enter(self):
        greeting = "Greet the user warmly and ask how you can help with P2P lending."
        self.metrics_collector.add_conversation_entry("system", greeting)
        try:
            await self.session.generate_reply(instructions=greeting)
        except Exception as e:
            logging.getLogger(__name__).error(f"Error generating greeting: {e}")

    # Event handlers will capture conversation data automatically


def prewarm(proc: JobProcess):
    pass


async def entrypoint(ctx: JobContext):
    logger = logging.getLogger(__name__)
    logger.info("🚀 Starting agent entrypoint...")
    try:
        ctx.log_context_fields = {"room": ctx.room.name}
        session_name = ctx.room.name or f"session_{int(time.time())}"
        metrics_collector = SessionMetricsCollector(session_name)

        global log_file_path
        log_file_path = os.path.join(LOGS_DIR, f"{session_name}.log")
        file_handler = logging.FileHandler(log_file_path)
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(name)s - %(message)s'))
        file_handler.addFilter(ImportantLogsFilter())
        logging.getLogger().addHandler(file_handler)

        global _metrics_collector
        _metrics_collector = metrics_collector

        bot = NeuraVoiceAgent(metrics_collector=metrics_collector)
        session = AgentSession()
        usage_collector = metrics.UsageCollector()

        # Use the proven approach from livekit_agent.py with duplication prevention
        conversation_logged = set()  # Prevent duplicates
        
        @session.on("conversation_item_added")
        def _on_conversation_item_added(event):
            try:
                item = getattr(event, "item", None)
                if not item:
                    return
                
                role = getattr(item, "role", "")
                content = _extract_text_content(getattr(item, "content", []))
                speech_id = getattr(item, "speech_id", None)
                
                if content and role in ["user", "assistant"]:
                    # Prevent duplicate logging
                    entry_hash = hash((role, content[:100], speech_id))
                    if entry_hash not in conversation_logged:
                        conversation_logged.add(entry_hash)
                        metrics_collector.add_conversation_entry(role, content, speech_id)
                        logger.info(f"📝 {role.title()}: {content[:50]}...")
                    
            except Exception as e:
                logger.error(f"Conversation logging error: {e}")

        def _extract_text_content(content):
            """Extract text from content (optimized version from livekit_agent.py)."""
            if isinstance(content, str):
                return content.strip()
            elif isinstance(content, list):
                return "".join(str(part.get("text", part) if isinstance(part, dict) 
                                  else getattr(part, "text", str(part))) for part in content).strip()
            return str(content).strip() if content else ""

        # Fallback handlers for cases where conversation_item_added doesn't fire
        @session.on("user_speech_committed") 
        def _fallback_user_speech(event):
            transcript = getattr(event, 'transcript', None)
            speech_id = getattr(event, 'speech_id', None)
            if transcript and transcript.strip():
                entry_hash = hash(("user", transcript[:100], speech_id))
                if entry_hash not in conversation_logged:
                    conversation_logged.add(entry_hash)
                    metrics_collector.add_conversation_entry("user", transcript.strip(), speech_id)
                    logger.info(f"📝 User (fallback): {transcript[:50]}...")

        # Additional fallback event handlers for comprehensive capture
        @session.on("user_speech_final")
        def _fallback_user_final(event):
            transcript = getattr(event, 'transcript', None)
            speech_id = getattr(event, 'speech_id', None)
            if transcript and transcript.strip():
                entry_hash = hash(("user", transcript[:100], speech_id))
                if entry_hash not in conversation_logged:
                    conversation_logged.add(entry_hash)
                    metrics_collector.add_conversation_entry("user", transcript.strip(), speech_id)
                    logger.info(f"📝 User (final): {transcript[:50]}...")

        @session.on("agent_speech_final")
        def _fallback_agent_final(event):
            try:
                # Try multiple ways to extract content
                content = None
                if hasattr(event, 'item') and event.item:
                    content = _extract_text_content(getattr(event.item, 'content', ''))
                elif hasattr(event, 'content'):
                    content = _extract_text_content(event.content)
                
                speech_id = getattr(event, 'speech_id', None)
                if content and content.strip():
                    entry_hash = hash(("assistant", content[:100], speech_id))
                    if entry_hash not in conversation_logged:
                        conversation_logged.add(entry_hash)
                        metrics_collector.add_conversation_entry("assistant", content.strip(), speech_id)
                        logger.info(f"📝 Assistant (final): {content[:50]}...")
            except Exception as e:
                logger.error(f"Fallback agent final error: {e}")

        # Collect metrics
        @session.on("metrics_collected")
        def on_metrics_collected(event: MetricsCollectedEvent):
            metrics.log_metrics(event.metrics)
            usage_collector.collect(event.metrics)

        # Remove debug handlers to prevent noise and duplication

        async def log_usage():
            summary = usage_collector.get_summary()
            metrics_collector.set_usage_summary(str(summary))
            logger.info(f"📊 Final usage: {summary}")

        async def save_metrics():
            await asyncio.to_thread(metrics_collector.save_to_json)

        ctx.add_shutdown_callback(log_usage)
        ctx.add_shutdown_callback(save_metrics)

        # Inactivity handler
        inactivity_task = None
        async def user_presence_task():
            logger.warning("User inactive. Checking presence...")
            for i in range(3):
                await session.generate_reply(instructions="Politely check if user is still present.")
                await asyncio.sleep(15)
            await asyncio.shield(session.aclose())
            await save_metrics()
            await ctx.room.disconnect()

        @session.on("user_state_changed")
        def on_user_state_changed(ev: UserStateChangedEvent):
            nonlocal inactivity_task
            if ev.new_state == "away":
                inactivity_task = asyncio.create_task(user_presence_task())
            elif inactivity_task:
                inactivity_task.cancel()
                inactivity_task = None

        asyncio.create_task(get_vector_db_loader())

        await session.start(
            agent=bot,
            room=ctx.room,
            room_input_options=RoomInputOptions(noise_cancellation=noise_cancellation.BVC()),
            room_output_options=RoomOutputOptions(transcription_enabled=True),
        )

        background_audio = BackgroundAudioPlayer(
            ambient_sound=AudioConfig(BuiltinAudioClip.OFFICE_AMBIENCE, volume=0.6),
        )
        await background_audio.start(room=ctx.room, agent_session=session)

    except Exception as e:
        logger.error(f"❌ CRITICAL ERROR in entrypoint: {e}", exc_info=True)
        raise


if __name__ == "__main__":
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            job_memory_warn_mb=max(MEMORY_WARN_MB, 1024),
            prewarm_fnc=prewarm,
            job_memory_limit_mb=0,
            initialize_process_timeout=25
        )
    )