"""
Minimal and clean script to build a Milvus vector database with:
- Embeddings generated ONLY from the 'question' (not answer or formatted text)
- Stores: question_embedding (vector), question_answer (formatted string for LLM)
- Logs Novita API usage
- Reuses existing DB if already built
"""

import os
import time
import logging
from pathlib import Path
from typing import List
import dotenv
import pandas as pd
from openai import OpenAI
from pymilvus import MilvusClient, FieldSchema, CollectionSchema, DataType
from dotenv import load_dotenv



# Setup logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s | %(levelname)s | %(message)s")
logger = logging.getLogger(__name__)

load_dotenv()

# Configuration
DOCS_DIR = "clients/docs"
# MILVUS_DB_PATH = str((Path(__file__).parent / "knowledgebase_2.db").resolve())
MILVUS_DB_PATH = 'clients/knowledgebase.db'
COLLECTION_NAME = "p2p_kb"
QUESTION_COL = "question"
ANSWER_COL = "answer"
BATCH_SIZE = 100
DIMENSION = 3072
EMBEDDING_MODEL = "text-embedding-3-large"
OPENAI_API_KEY = dotenv.get_key(".env", "OPENAI_API_KEY")

if not OPENAI_API_KEY:
    raise EnvironmentError("OPENAI_API_KEY environment variable is not set.")


class VectorDBBuilder:
    """
    Builds a Milvus vector DB where:
    - Embedding is generated ONLY from the 'question'
    - Stores only 'question_embedding' and 'question_answer' (formatted)
    """

    def __init__(self):
        self.client = MilvusClient(uri=MILVUS_DB_PATH)
        self.openai_client = OpenAI(api_key=OPENAI_API_KEY)

    def _load_and_format_qa(self) -> List[str]:
        """Load Q&A and return list of formatted answer strings, but keep questions separate for embedding."""
        docs_path = Path(DOCS_DIR)
        file = next(docs_path.glob("*.csv"), None) or next(docs_path.glob("*.xlsx"), None)
        if not file:
            raise FileNotFoundError(f"No CSV or Excel file found in '{DOCS_DIR}'")

        logger.info(f"📊 Loading Q&A data from: {file}")
        df = pd.read_csv(file) if file.suffix == ".csv" else pd.read_excel(file)

        required = [QUESTION_COL, ANSWER_COL]
        missing = [col for col in required if col not in df.columns]
        if missing:
            raise ValueError(f"Missing required columns: {missing}")

        # Extract and clean questions and answers
        questions = df[QUESTION_COL].astype(str).str.strip().tolist()
        answers = df[ANSWER_COL].astype(str).str.strip().tolist()

        # Format Q&A for LLM context (this is NOT used for embedding)
        formatted_texts = [
            f"📌 Question: {q}\n💡 Answer: {a}" for q, a in zip(questions, answers)
        ]

        return questions, formatted_texts  # Return both: questions for embedding, formatted for storage

    def _is_db_ready(self) -> bool:
        """Check if Milvus DB and collection already exist and have data."""
        if not Path(MILVUS_DB_PATH).exists():
            return False

        if not self.client.has_collection(COLLECTION_NAME):
            return False

        try:
            stats = self.client.get_collection_stats(collection_name=COLLECTION_NAME)
            row_count = int(stats.get("row_count", 0))
            return row_count > 0
        except Exception as e:
            logger.warning(f"⚠️ Could not get collection stats: {e}")
            return False

    def _create_collection(self):
        """Create collection with only two fields."""
        if self.client.has_collection(COLLECTION_NAME):
            logger.info(f"🗑️ Dropping existing collection: {COLLECTION_NAME}")
            self.client.drop_collection(COLLECTION_NAME)

        fields = [
            FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
            FieldSchema(name="question_answer", dtype=DataType.VARCHAR, max_length=10000),
            FieldSchema(name="question_embedding", dtype=DataType.FLOAT_VECTOR, dim=DIMENSION),
        ]
        schema = CollectionSchema(fields=fields, description="Q&A for semantic search")
        self.client.create_collection(collection_name=COLLECTION_NAME, schema=schema)
        logger.info(f"✅ Collection '{COLLECTION_NAME}' created.")

    def _create_index(self):
        """Create FLAT index on embedding field."""
        index_params = self.client.prepare_index_params()
        index_params.add_index(
            field_name="question_embedding",
            index_type="FLAT",
            metric_type="L2",
        )
        self.client.create_index(collection_name=COLLECTION_NAME, index_params=index_params, sync=True)
        logger.info("✅ Index created on 'question_embedding'.")

    def build(self):
        """Build vector DB using embeddings from questions only."""
        if self._is_db_ready():
            logger.info(f"✅ Vector database '{MILVUS_DB_PATH}' already exists and is populated. Skipping rebuild.")
            return

        logger.info("🧠 Building vector database...")
        questions, formatted_texts = self._load_and_format_qa()

        # ✅ Generate embeddings from QUESTIONS only
        logger.info("🔐 Generating embeddings from 'question' only (not answer or context)")
        embeddings = []
        for i in range(0, len(questions), BATCH_SIZE):
            batch = questions[i:i + BATCH_SIZE]
            logger.info(f"🌐 Calling OpenAI API for batch of {len(batch)} questions...")
            start_time = time.time()

            try:
                response = self.openai_client.embeddings.create(
                    model=EMBEDDING_MODEL,
                    input=batch,
                    encoding_format="float"
                )
                batch_embs = [data.embedding for data in response.data]
                embeddings.extend(batch_embs)
                elapsed = time.time() - start_time
                logger.info(f"✅ Got {len(batch_embs)} embeddings in {elapsed:.2f}s")
            except Exception as e:
                logger.error(f"❌ OpenAI API call failed: {e}")
                raise

        # Setup collection and index
        self._create_collection()
        self._create_index()

        # Insert: embedding from question, text from formatted Q&A
        data = [
            {"question_answer": fmt, "question_embedding": emb}
            for fmt, emb in zip(formatted_texts, embeddings)
        ]
        self.client.insert(collection_name=COLLECTION_NAME, data=data)
        logger.info(f"✅ Inserted {len(data)} records into '{COLLECTION_NAME}'.")

    def search(self, query: str, top_k: int = 2) -> List[str]:
        """Search and return formatted Q&A strings (context for LLM)."""
        logger.info(f"🔍 Searching for: '{query}' (top_k={top_k})")

        # ✅ Embed the query as a "question"
        logger.info("🌐 Generating embedding for query (treated as question)...")
        try:
            response = self.openai_client.embeddings.create(
                model=EMBEDDING_MODEL,
                input=query,
                encoding_format="float"
            )
            query_embedding = response.data[0].embedding
        except Exception as e:
            logger.error(f"❌ Failed to generate query embedding: {e}")
            raise

        # Search
        results = self.client.search(
            collection_name=COLLECTION_NAME,
            data=[query_embedding],
            anns_field="question_embedding",
            limit=top_k,
            output_fields=["question_answer"],
            search_params={"metric_type": "L2"},
        )

        hits = [hit.entity.get("question_answer") for hit in results[0]]
        logger.info(f"✅ Found {len(hits)} result(s)")
        return hits

    def close(self):
        """Close Milvus connection."""
        self.client.close()
        logger.info("🔌 Milvus connection closed.")


# ————————————————————————
#   USAGE
# ————————————————————————
if __name__ == "__main__":
    import time  # Import here to avoid unused if imported as module

    db = VectorDBBuilder()
    db.build()

    # Test search
    test_query = "what is p2p lending"
    results = db.search(test_query, top_k=2)

    print("\n🔍 Search Results:")
    for i, context in enumerate(results, 1):
        print(f"\n{i}.\n{context}")

    db.close()