"""
Neura Voice P2P Lending Agent

This module provides a voice-enabled agent for P2P lending queries using
LlamaIndex for RAG (Retrieval-Augmented Generation) and vector search.
"""

# Standard library imports
import asyncio
import logging
import os
import sys
import time
from concurrent.futures import Future, ThreadPoolExecutor
from pathlib import Path
from typing import Annotated, Optional

# Third-party imports
import pandas as pd
from dotenv import load_dotenv
from tqdm import tqdm


from prompt import get_enhanced_system_prompt


from linkup import LinkupClient
from retrive import VectorDBLoader

from livekit.plugins.turn_detector.multilingual import MultilingualModel
from livekit.agents import Agent, AgentSession, AutoSubscribe, JobContext, WorkerOptions, cli, llm, UserStateChangedEvent, RunContext, metrics, MetricsCollectedEvent, JobProcess, AudioConfig, BackgroundAudioPlayer, BuiltinAudioClip
from livekit.plugins import openai,  noise_cancellation, google, silero
from livekit.agents.llm import Chat<PERSON><PERSON><PERSON><PERSON>, ChatMessage, function_tool
from livekit.agents.voice.room_io import RoomInputOptions, RoomOutputOptions


# Note: These imports need to be added based on your actual framework
# from your_framework import Agent, ChatContext, function_tool
# from linkup_client import LinkupClient

# Load environment variables
load_dotenv()

# Constants
LOGS_DIR = "logs"
PERSIST_DIR = "./neura-voice-p2p-engine-storage"
DOCS_DIR = "docs"
QUESTION_COL = "question"
ANSWER_COL = "answer"
BATCH_SIZE = 100
MEMORY_WARN_MB = 1024

# Logging setup
os.makedirs(LOGS_DIR, exist_ok=True)
log_file_path = os.path.join(
    LOGS_DIR, f"nv_demo_{time.strftime('%Y%m%d-%H%M%S')}.log"
)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file_path),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

_vector_db_loader: Optional[VectorDBLoader] = None

async def get_vector_db_loader() -> VectorDBLoader:
    """Lazily initialize VectorDBLoader in a background thread and return the instance."""
    global _vector_db_loader
    if _vector_db_loader is None:
        loop = asyncio.get_running_loop()
        _vector_db_loader = await loop.run_in_executor(None, VectorDBLoader)
    return _vector_db_loader

@function_tool
async def search_knowledge_base(
    query: Annotated[
        str,
        """Convert user's query into a comprehensive English search query,
        optimized for vector similarity matching. Incorporate relevant conversation context, translate Hindi to English"""
    ]
) -> str:
    """
    Search the knowledge base for P2P lending information.
    Strictly use this tool to answer any question about Lenden Club, P2P 
    lending, investment, products, founders, financial service, etc. This should be the primary source of 
    information to answer any user query.
    
    Args:
        query: The search query string.
        
    Returns:
        str: Formatted Q&A pairs from the knowledge base.
    """
    loader = await get_vector_db_loader()
    results = await loader.search(query, top_k=2)
    if not results:
        return "I'm sorry, I couldn't find relevant information in the knowledge base."
    qa_texts = [f"{idx}. {item.get('formatted_text', '').strip()}" for idx, item in enumerate(results, start=1)]
    return "\n\n".join(qa_texts)



@function_tool
async def web_search(
    query: Annotated[
        str,
        "Make query as specific as possible with additional context. "
        "Transform 'what's the weather' to 'what is the weather in [city] "
        "today'. Add relevant constraints, dates, or location details for "
        "better results."
    ]
) -> str:
    """
    Search web for real-time information.
    
    Search for current events, news, weather, stock prices, or recent data 
    not in knowledge base. Called when user asks to search web, google 
    something, or requests current/live information.
    
    Args:
        query: The web search query string.
        
    Returns:
        str: The search result or error message.
    """
    try:
        api_key = os.getenv('LINKUP_API_KEY')
        client = LinkupClient(api_key=api_key)
        
        def do_search():
            return client.search(
                query=query,
                depth="standard",
                output_type="sourcedAnswer",
            )
        
        loop = asyncio.get_running_loop()
        response = await loop.run_in_executor(None, do_search)
        
        if hasattr(response, 'answer') and response.answer:
            return response.answer
        else:
            return "No direct answer found. Try rephrasing your question."
    
    except Exception as e:
        logger.error(f"Web search error: {e}")
        return "Web search error. Please try again later."


class NeuraVoiceAgent(Agent):
    """
    Main voice agent for P2P lending conversations.
    
    Handles conversations with users, retrieves information from the 
    knowledge base, and answers queries about P2P lending.
    """
    
    def __init__(self, chat_ctx: ChatContext) -> None:
        """
        Initialize the Neura Voice Agent.
        
        Args:
            chat_ctx: The chat context for the conversation.
        """
        super().__init__(
            instructions=get_enhanced_system_prompt(),
            chat_ctx=chat_ctx,
            allow_interruptions=True,
            tools=[
                search_knowledge_base, 
                web_search
            ]
        )
    

    async def on_enter(self) -> None:
        """Handle agent initialization and greeting."""
        greeting_instruction = (
            'Greet the user warmly and ask them how you can help them '
            'today about p2p lending.'
        )
        await self.session.generate_reply(instructions=greeting_instruction)
        
def prewarm(proc: JobProcess):
        proc.userdata["vad"] = silero.VAD.load()       
   
async def entrypoint(ctx: JobContext):
    logger.info("🚀 Starting agent entrypoint...")
    try:
        
        ctx.log_context_fields = {
        "room": ctx.room.name,
        }

        USER_AWAY_TIMEOUT_S = 15.0
        
        session = AgentSession(
            stt=openai.STT(model='gpt-4o-mini-transcribe'),
             vad=ctx.proc.userdata["vad"],
            llm=openai.LLM(model='gpt-4o-mini', temperature=0.6),
            tts=google.TTS(language='hi-IN',
                          turn_detector=MultilingualModel(),
                          gender='female',
                          voice_name='hi-IN-Chirp3-HD-Laomedeia',
                          sample_rate=24000,
                          speaking_rate=0.95,
                          use_streaming=True,
                        #   audio_encoding=texttospeech.AudioEncoding.LINEAR16,
                          location='asia-southeast1',
                          credentials_file=os.getenv('GOOGLE_APPLICATION_CREDENTIALS')),
           
            # allow_interruptions=True,
            user_away_timeout=USER_AWAY_TIMEOUT_S,
        )
        # logger.info(f"✅ AgentSession created with user_away_timeout of {USER_AWAY_TIMEOUT_S}s.")
        
        # Track inactivity task
        inactivity_task: asyncio.Task | None = None
        
        async def user_presence_task():
            """Check if user is still present after inactivity is detected"""
            logger.warning(f"User has been inactive for {USER_AWAY_TIMEOUT_S}s. Starting presence check...")
            
            try:
                # Try to ping the user 3 times, if we get no answer, close the session
                for i in range(3):
                    logger.info(f"Performing presence check #{i+1}/3...")
                    await session.generate_reply(
                        instructions="The user has been inactive. Politely check if the user is still present, based on the conversation history. Keep it very short."
                    )
                    await asyncio.sleep(10)
                
                logger.warning("User did not respond to presence checks. Ending the session.")
                await asyncio.shield(session.aclose())
                ctx.delete_room()
            except asyncio.CancelledError:
                logger.info("User presence check was cancelled - user likely returned")
                # Don't re-raise, this is expected behavior
            except Exception as e:
                logger.error(f"Error in user presence check: {e}")
                # Still try to close the session gracefully
                try:
                    await asyncio.shield(session.aclose())
                    ctx.delete_room()
                except Exception as cleanup_error:
                    logger.error(f"Error during session cleanup: {cleanup_error}")

        @session.on("user_state_changed")
        def _on_user_state_changed(ev: UserStateChangedEvent):  # noqa: F841
            nonlocal inactivity_task
            if ev.new_state == "away":
                inactivity_task = asyncio.create_task(user_presence_task())
                return
            if inactivity_task is not None:
                try:
                    inactivity_task.cancel()
                except Exception as e:
                    logger.debug(f"Error cancelling inactivity task: {e}")
                finally:
                    inactivity_task = None

        # log metrics as they are emitted, and total usage after session is over
        usage_collector = metrics.UsageCollector()
        @session.on("metrics_collected")
        def _on_metrics_collected(event: MetricsCollectedEvent):  # noqa: F841
            metrics.log_metrics(event.metrics)
            usage_collector.collect(event.metrics)
            
        async def log_usage():
            summary = usage_collector.get_summary()
            logger.info(f"Usage: {summary}")

        logger.info("✅ Event handlers configured.")
        
        # shutdown callbacks are triggered when the session is over
        ctx.add_shutdown_callback(log_usage)
        
        bot = NeuraVoiceAgent(chat_ctx=ChatContext())
        logger.info("✅ NeuraVoiceAgent created")
        asyncio.create_task(get_vector_db_loader())
        
        await session.start(
            agent=bot,
            room=ctx.room,
            room_input_options=RoomInputOptions(
                noise_cancellation=noise_cancellation.BVC()
            ),
            room_output_options=RoomOutputOptions(
                transcription_enabled=True,
            )
        )
        
        # Add background ambient audio
        background_audio = BackgroundAudioPlayer(
            ambient_sound=AudioConfig(BuiltinAudioClip.OFFICE_AMBIENCE, volume=0.7),
        )
        await background_audio.start(room=ctx.room, agent_session=session)
        
        logger.info("✅ Agent session started with background audio.")
        
    except asyncio.CancelledError:
        logger.info("Agent session was cancelled")
        # Don't re-raise, this is expected during shutdown
    except Exception as e:
        logger.error(f"Error in entrypoint: {e}")
        raise
    
    
if __name__ == "__main__":
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            # Raise memory warning threshold to reduce noisy alerts.
            job_memory_warn_mb=max(MEMORY_WARN_MB, 1024),
            prewarm_fnc=prewarm,
            # Keep unlimited but warn only when threshold exceeded.
            job_memory_limit_mb=0,
            initialize_process_timeout=25
        )
    )



