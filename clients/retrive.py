# vector_db_loader.py
import asyncio
import logging
from pathlib import Path
from typing import List, Dict
from concurrent.futures import ThreadPoolExecutor
from openai import OpenAI
from pymilvus import MilvusClient
import dotenv

dotenv.load_dotenv()

# Configuration
# MILVUS_DB_PATH = str((Path(__file__).parent / "knowledgebase_2.db").resolve())
MILVUS_DB_PATH = 'clients/knowledgebase.db'
COLLECTION_NAME = "p2p_kb"
DIMENSION = 3072
EMBEDDING_MODEL = "text-embedding-3-large"
OPENAI_API_KEY = dotenv.get_key(".env", "OPENAI_API_KEY")

# Setup logger
logger = logging.getLogger(__name__)

class VectorDBLoader:
    """
    Asynchronously loads and searches a pre-built Milvus vector DB.
    Uses thread pool for safe async embedding and Milvus operations.
    """
    def __init__(self, db_path: str = MILVUS_DB_PATH, collection: str = COLLECTION_NAME):
        self.db_path = db_path
        self.collection_name = collection
        self.client: MilvusClient = None
        self.openai_client: OpenAI = None
        self.executor = ThreadPoolExecutor(max_workers=4)
        self._init_clients()

    def _init_clients(self):
        """Initialize Milvus and OpenAI clients (sync, called during init)."""
        try:
            # Load API key
            api_key = OPENAI_API_KEY
            if not api_key:
                raise EnvironmentError("OPENAI_API_KEY not found in .env")

            # Initialize clients
            self.client = MilvusClient(uri=self.db_path)
            self.openai_client = OpenAI(api_key=api_key)
            logger.info("✅ Milvus and OpenAI clients initialized.")
        except Exception as e:
            logger.error(f"❌ Failed to initialize clients: {e}")
            raise

    async def is_ready(self) -> bool:
        """Check if DB and collection are ready (async-safe)."""
        def _sync_check():
            if not Path(self.db_path).exists():
                return False
            if not self.client.has_collection(self.collection_name):
                return False
            stats = self.client.get_collection_stats(self.collection_name)
            return int(stats.get("row_count", 0)) > 0

        try:
            return await asyncio.get_running_loop().run_in_executor(self.executor, _sync_check)
        except Exception as e:
            logger.warning(f"⚠️ DB not ready: {e}")
            return False

    async def _embed_texts(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings using OpenAI API in a thread-safe way."""
        def _sync_embed():
            try:
                response = self.openai_client.embeddings.create(
                    model=EMBEDDING_MODEL,
                    input=texts,
                    encoding_format="float"
                )
                return [data.embedding for data in response.data]
            except Exception as e:
                logger.error(f"❌ Embedding failed: {e}")
                raise

        return await asyncio.get_running_loop().run_in_executor(self.executor, _sync_embed)

    async def search(self, query: str, top_k: int = 2) -> List[Dict[str, str]]:
        """
        Search the vector DB using async-safe embedding and search.
        Returns top-k formatted Q&A results.
        """
        if not await self.is_ready():
            logger.error("❌ Vector DB is not ready or missing.")
            return []

        # Step 1: Embed query
        try:
            query_embedding = (await self._embed_texts([query]))[0]
        except Exception as e:
            logger.error(f"❌ Failed to generate embedding for query: {e}")
            return []

        # Step 2: Search (sync but offloaded)
        def _sync_search():
            try:
                results = self.client.search(
                    collection_name=self.collection_name,
                    data=[query_embedding],
                    anns_field="question_embedding",
                    limit=top_k,
                    output_fields=["question_answer"],
                    search_params={"metric_type": "L2"},
                )
                return [
                    {"formatted_text": hit.entity.get("question_answer")}
                    for hit in results[0]
                ]
            except Exception as e:
                logger.error(f"❌ Milvus search failed: {e}")
                return []

        return await asyncio.get_running_loop().run_in_executor(self.executor, _sync_search)

    def close(self):
        """Shutdown executor and close connections."""
        if self.client:
            self.client.close()
        self.executor.shutdown(wait=True)
        logger.info("🔌 VectorDBLoader closed gracefully.")
        
        
if __name__ == "__main__":
    import asyncio

    async def _main():
        db_loader = VectorDBLoader()
        results = await db_loader.search("What is the interest rate for Lenden Club?")
        print(results)
        db_loader.close()

    asyncio.run(_main())