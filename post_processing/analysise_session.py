"""
query_analyzer.py

A robust class to analyze user-bot conversations from a CSV file,
format them into JSON for LLM analysis, classify query types using an async LLM client,
and write the structured analysis back into the same CSV file.
"""

import asyncio
import csv
import json
from typing import List, Dict, Any, Optional
from pathlib import Path

# Assuming these are correctly defined in separate modules
from prompts import analyse_query_type, analyse_query_type_user_prompt
from llm_client import AsyncLLMClient


class ConversationAnalyzer:
    """
    A class to load, parse, analyze, and enrich user-bot conversations from a CSV file.
    Converts conversation data into JSON for LLM-based query classification and writes
    the analysis results back into the same CSV.
    """

    def __init__(self, csv_file_path: str, llm_client: Optional[AsyncLLMClient] = None):
        """
        Initialize the analyzer.

        Args:
            csv_file_path (str): Path to the input/output CSV file.
            llm_client (AsyncLLMClient, optional): LLM client instance.
        """
        self.csv_file_path = Path(csv_file_path)
        self.llm_client = llm_client or AsyncLLMClient()
        self.conversations: List[Dict[str, str]] = []
        self.formatted_conversation: Dict[str, List[Dict[str, str]]] = {"conversation": []}

    def _load_csv_data(self) -> None:
        """
        Load conversation data from the CSV file.

        Raises:
            FileNotFoundError: If the CSV file does not exist.
            ValueError: If required columns are missing.
        """
        if not self.csv_file_path.exists():
            raise FileNotFoundError(f"CSV file not found: {self.csv_file_path}")

        with open(self.csv_file_path, mode='r', encoding='utf-8', newline='') as file:
            reader = csv.DictReader(file)
            if not reader.fieldnames:
                raise ValueError("CSV file is empty or has no headers.")
            required_columns = {"user_query", "bot_response"}  # Updated column name
            if not required_columns.issubset(reader.fieldnames):
                raise ValueError(
                    f"CSV must contain columns: {required_columns}. Found: {reader.fieldnames}"
                )

            self.conversations = [
                {"user": row["user_query"].strip(), "bot": row["bot_response"].strip()}
                for row in reader
                if row["user_query"].strip() and row["bot_response"].strip()
            ]

    def _format_for_llm(self) -> str:
        """
        Format the conversation into the required JSON structure for LLM input.

        Returns:
            str: Pretty-printed JSON string of the conversation.
        """
        self.formatted_conversation["conversation"] = []

        for exchange in self.conversations:
            self.formatted_conversation["conversation"].append({
                "speaker": "user",
                "message": exchange["user"]
            })
            self.formatted_conversation["conversation"].append({
                "speaker": "bot",
                "message": exchange["bot"]
            })

        return json.dumps(self.formatted_conversation, ensure_ascii=False, indent=2)

    def prepare_input_for_llm(self) -> str:
        """
        Public method to load CSV and return formatted JSON string for LLM.

        Returns:
            str: Formatted JSON string ready for prompt injection.
        """
        self._load_csv_data()
        return self._format_for_llm()

    async def analyze_with_llm(self) -> Optional[Dict[str, Any]]:
        """
        Analyze the conversation using the LLM.

        Returns:
            Optional[Dict]: Parsed JSON response from LLM (assumed valid).
        """
        try:
            json_conversation = self.prepare_input_for_llm()
            user_prompt = analyse_query_type_user_prompt().replace(
                "{{conversation_data}}", json_conversation
            )
            system_prompt = analyse_query_type()

            response = await self.llm_client.simple_query(
                user_message=user_prompt,
                system_message=system_prompt,
                temperature=0.2,
                max_tokens=2048,
                response_format={"type": "json_object"}  # Ensure JSON output
            )

            if not response:
                print("No response from LLM.")
                return None

            # Since LLM client guarantees JSON, we trust the response
            return json.loads(response.strip())
        except Exception as e:
            print(f"Error during LLM analysis or JSON parsing: {e}")
            return None

    def write_analysis_to_csv(self, analysis_data: Dict[str, Any]) -> None:
        """
        Write the LLM analysis results back into the same CSV file.

        Adds the following columns:
            - query_type
            - sentiment
            - intent
            - confidence
            - conversation_summary (as JSON string)

        Args:
            analysis_data (Dict): The parsed JSON response from the LLM.
        """
        # Read original data again
        with open(self.csv_file_path, mode='r', encoding='utf-8', newline='') as infile:
            reader = csv.DictReader(infile)
            rows = list(reader)
            original_fieldnames = reader.fieldnames or []

        # Define new fieldnames (with updated user column)
        enriched_fieldnames = original_fieldnames + [
            "query_type", "sentiment", "intent", "confidence"
        ]

        # Create a map from user query to analysis
        analysis_map = {
            item["user_query"]: {
                "query_type": item.get("query_type", "Unknown"),
                "sentiment": item.get("sentiment", "Unknown"),
                "intent": item.get("intent", "Unknown"),
                "confidence": item.get("confidence", "Unknown")
            }
            for item in analysis_data.get("analysis", [])
        }

        # Add summary as a single JSON string
        summary = analysis_data.get("conversation_summary", {})
        summary_str = json.dumps(summary, ensure_ascii=False)

        # Write back to the same file
        with open(self.csv_file_path, mode='w', encoding='utf-8', newline='') as outfile:
            writer = csv.DictWriter(outfile, fieldnames=enriched_fieldnames + ["conversation_summary"])
            writer.writeheader()

            for row in rows:
                user_query = row["user_query"].strip()  # Updated field name
                analysis = analysis_map.get(user_query, {
                    "query_type": "Unknown",
                    "sentiment": "Unknown",
                    "intent": "Unknown",
                    "confidence": "Unknown"
                })
                enriched_row = {
                    **row,
                    "query_type": analysis["query_type"],
                    "sentiment": analysis["sentiment"],
                    "intent": analysis["intent"],
                    "confidence": analysis["confidence"],
                    "conversation_summary": summary_str
                }
                writer.writerow(enriched_row)

        print(f"✅ Analysis results written back to: {self.csv_file_path}")


# ---------------------------
# Example Usage
# ---------------------------

async def main():
    """Example usage of the ConversationAnalyzer class."""
    csv_path = "metrics/playground-BTpR-I7up_metrics_20250805_092604.csv"

    analyzer = ConversationAnalyzer(csv_path)

    print("Analyzing conversation with LLM...")
    result = await analyzer.analyze_with_llm()

    if result:
        print("\n✅ LLM Analysis Result (Parsed):")
        print(json.dumps(result, indent=2, ensure_ascii=False))

        print("\n📝 Writing analysis back to CSV...")
        analyzer.write_analysis_to_csv(result)
    else:
        print("\n❌ Failed to get analysis from LLM.")


if __name__ == "__main__":
    asyncio.run(main())