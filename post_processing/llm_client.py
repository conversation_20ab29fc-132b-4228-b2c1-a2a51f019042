# async_llm_client.py

import asyncio
import os
import random
import json
from typing import Optional, Dict, Any, List
from groq import AsyncGroq


class AsyncLLMClient:
    """
    Async client for Groq LLM API with chat completions only.
    """
    
    # List of Groq API keys for rotation
    GROQ_API_KEY_LIST = [
        '********************************************************',
        '********************************************************',
        '********************************************************',
        '********************************************************',
        '********************************************************',
        '********************************************************',
        '********************************************************',
        '********************************************************'
    ]

    def __init__(
        self,
        api_key: Optional[str] = None,
        model_name: str = "llama-3.3-70b-versatile",
        temperature: float = 0.1,
        max_tokens: int = 1024,
        top_p: float = 1.0
    ):
        """
        Initialize the async LLM client.
        
        Args:
            api_key: Groq API key. If None, uses random key from list or env variable.
            model_name: Model to use for completions.
            temperature: Controls randomness (0.0-1.0).
            max_tokens: Maximum tokens to generate.
            top_p: Nucleus sampling parameter.
        """
        self.api_key = api_key or os.environ.get("GROQ_API_KEY") or self._get_random_api_key()
        if not self.api_key:
            raise ValueError("Groq API key is required")
            
        self.model_name = model_name
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.top_p = top_p
        
        # Initialize async client
        self.client = AsyncGroq(api_key=self.api_key)

    def _get_random_api_key(self) -> str:
        """Get a random API key from the list."""
        return random.choice(self.GROQ_API_KEY_LIST)

    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        top_p: Optional[float] = None,
        stop: Optional[str] = None,
        response_format: Optional[Dict[str, str]] = None
    ) -> Optional[str]:
        """
        Create a chat completion using the async Groq client.
        
        Args:
            messages: List of message dicts with 'role' and 'content' keys.
            temperature: Override default temperature.
            max_tokens: Override default max_tokens.
            top_p: Override default top_p.
            stop: Stop sequence for generation.
            response_format: Format of response (e.g., {"type": "json_object"}).
            
        Returns:
            Generated text content as JSON string or None if error.
        """
        if not messages or not isinstance(messages, list):
            print("Invalid messages format: must be a non-empty list")
            return None
            
        try:
            params = {
                "messages": messages,
                "model": self.model_name,
                "temperature": temperature if temperature is not None else self.temperature,
                "max_completion_tokens": max_tokens if max_tokens is not None else self.max_tokens,
                "top_p": top_p if top_p is not None else self.top_p,
                "stop": stop,
                "stream": False
            }

            if response_format:
                params["response_format"] = response_format

            chat_completion = await self.client.chat.completions.create(**params)
            
            if chat_completion.choices and len(chat_completion.choices) > 0:
                content = chat_completion.choices[0].message.content
                return content  # Already a JSON string when response_format is json_object
            else:
                print("No response content received")
                return None
                
        except Exception as e:
            print(f"Error calling Groq API: {e}")
            return None

    async def simple_query(
        self,
        user_message: str,
        system_message: str = "You are a helpful assistant.",
        response_format: Optional[Dict[str, str]] = None,
        **kwargs
    ) -> Optional[str]:
        """
        Simple query method for single user message.
        
        Args:
            user_message: The user's message.
            system_message: System prompt.
            response_format: Format of response (e.g., {"type": "json_object"}).
            **kwargs: Additional parameters for chat_completion.
            
        Returns:
            Generated response as JSON string or None if error.
        """
        messages = [
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_message}
        ]
        
        return await self.chat_completion(messages, response_format=response_format, **kwargs)


# Example usage
async def main():
    # Initialize client
    llm_client = AsyncLLMClient(
        model_name="llama-3.3-70b-versatile",
        temperature=0.5,
        max_tokens=1024
    )
    
    # Example 1: Direct chat completion with JSON response
    messages = [
        {"role": "system", "content": """You are a data analysis API that performs sentiment analysis on text.
        Respond only with JSON using this format:
        {
            "sentiment_analysis": {
            "sentiment": "positive|negative|neutral",
            "confidence_score": 0.95,
            "key_phrases": [
                {
                "phrase": "detected key phrase",
                "sentiment": "positive|negative|neutral"
                }
            ],
            "summary": "One sentence summary of the overall sentiment"
            }
        }"""}, 
        {"role": "user", "content": "Analyze the sentiment of this customer review: 'I absolutely love this product! The quality exceeded my expectations, though shipping took longer than expected.'"}
    ]
    
    response = await llm_client.chat_completion(
        messages=messages,
        response_format={"type": "json_object"}
    )
    print("Response:", response)
    
    # Example 2: Simple query with JSON output
    simple_response = await llm_client.simple_query(
        "Analyze the sentiment of: 'The product is okay, but customer service was terrible.'",
        system_message="""You are a data analysis API that performs sentiment analysis on text.
        Respond only with JSON using this format:
        {
            "sentiment_analysis": {
            "sentiment": "positive|negative|neutral",
            "confidence_score": 0.95,
            "key_phrases": [
                {
                "phrase": "detected key phrase",
                "sentiment": "positive|negative|neutral"
                }
            ],
            "summary": "One sentence summary of the overall sentiment"
            }
        }""",
        response_format={"type": "json_object"}
    )
    print("Simple Response:", simple_response)


if __name__ == "__main__":
    asyncio.run(main())