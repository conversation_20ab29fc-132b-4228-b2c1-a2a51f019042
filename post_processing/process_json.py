import json
import csv
from datetime import datetime
import os
import pytz
import re

class PostProcess:
    def __init__(self, json_file_path):
        self.json_file_path = json_file_path
        self.data = self._load_json()
        self.session_info = self.data.get("session_info", {})
        self.conversation = self.data.get("structured_conversation", [])
        self.usage_summary_str = self.data.get("usage_summary", "")
        self.ist_tz = pytz.timezone("Asia/Kolkata")

    def _load_json(self):
        """Load JSON data from file."""
        with open(self.json_file_path, 'r', encoding='utf-8') as f:
            return json.load(f)

    def _convert_to_ist(self, dt_str):
        """Convert ISO datetime string to IST string."""
        if not dt_str:
            return ""
        dt = datetime.fromisoformat(dt_str.replace("Z", "+00:00"))
        dt_ist = dt.astimezone(self.ist_tz)
        return dt_ist.strftime("%Y-%m-%d %H:%M:%S")

    def _extract_tool_calls(self, function_calls):
        """Extract tool calls as a readable string."""
        if not function_calls:
            return ""
        tools = []
        for call in function_calls:
            tool_str = f"{call.get('function_name')}({call.get('arguments', {})})"
            tools.append(tool_str)
        return " | ".join(tools)

    def _extract_context(self, function_calls):
        """
        Extract 'query' and 'result' only from 'search_knowledge_base' tool calls.
        Format: "Query: ... | Result: ..."
        """
        if not function_calls:
            return ""
        contexts = []
        for call in function_calls:
            if call.get("function_name") == "search_knowledge_base":
                query = call["arguments"].get("query", "").strip()
                result = call.get("result", "").strip()
                result = result.replace("\n", " ").replace("  ", " ")
                contexts.append(f"Query: {query} | Result: {result}")
        return " || ".join(contexts)

    def _extract_stt_metrics(self, stt_list):
        """Extract only duration and audio_duration from STT metrics."""
        if not stt_list:
            return {"stt_duration": "", "stt_audio_duration": ""}
        stt = stt_list[0]  # Use first entry
        return {
            "stt_duration": f"{stt.get('duration', ''):.6f}" if stt.get('duration') else "",
            "stt_audio_duration": f"{stt.get('audio_duration', ''):.6f}" if stt.get('audio_duration') else ""
        }

    def _extract_llm_metrics(self, llm_list):
        """Extract specific LLM metrics. If multiple, aggregate values."""
        if not llm_list:
            return {
                "llm_duration": "",
                "llm_ttft": "",
                "llm_prompt_tokens": "",
                "llm_completion_tokens": "",
                "llm_tokens_per_second": ""
            }

        total_duration = 0
        total_ttft = 0
        total_prompt = 0
        total_completion = 0
        total_tps = 0
        count = len(llm_list)

        for m in llm_list:
            total_duration += m.get("duration", 0)
            total_ttft += m.get("ttft", 0)
            total_prompt += m.get("prompt_tokens", 0)
            total_completion += m.get("completion_tokens", 0)
            tps = m.get("tokens_per_second", 0)
            total_tps += tps

        return {
            "llm_duration": f"{total_duration:.6f}",
            "llm_ttft": f"{total_ttft / count:.6f}" if count else "",
            "llm_prompt_tokens": total_prompt,
            "llm_completion_tokens": total_completion,
            "llm_tokens_per_second": f"{total_tps / count:.6f}" if count else ""
        }

    def _extract_tts_metrics(self, tts_list):
        """Extract specific TTS metrics."""
        if not tts_list:
            return {
                "tts_ttfb": "",
                "tts_duration": "",
                "tts_audio_duration": "",
                "tts_characters_count": "",
                "tts_cancelled": ""
            }
        tts = tts_list[0]
        return {
            "tts_ttfb": f"{tts.get('ttfb', ''):.6f}" if tts.get('ttfb') else "",
            "tts_duration": f"{tts.get('duration', ''):.6f}" if tts.get('duration') else "",
            "tts_audio_duration": f"{tts.get('audio_duration', ''):.6f}" if tts.get('audio_duration') else "",
            "tts_characters_count": tts.get("characters_count", ""),
            "tts_cancelled": str(tts.get("cancelled", "")).lower()
        }

    def _parse_usage_summary(self):
        """
        Parse usage_summary string into a dict.
        Example: UsageSummary(llm_prompt_tokens=18562, ...) -> {"llm_prompt_tokens": 18562, ...}
        """
        usage = {
            "total_llm_prompt_tokens": "",
            "total_llm_prompt_cached_tokens": "",
            "total_llm_completion_tokens": "",
            "total_tts_characters_count": "",
            "total_tts_audio_duration": "",
            "total_stt_audio_duration": ""
        }
        if not self.usage_summary_str:
            return usage

        # Extract key=value pairs using regex
        pattern = r"(\w+)=(\d+(?:\.\d+)?)"
        matches = re.findall(pattern, self.usage_summary_str)

        key_mapping = {
            "llm_prompt_tokens": "total_llm_prompt_tokens",
            "llm_prompt_cached_tokens": "total_llm_prompt_cached_tokens",
            "llm_completion_tokens": "total_llm_completion_tokens",
            "tts_characters_count": "total_tts_characters_count",
            "tts_audio_duration": "total_tts_audio_duration",
            "stt_audio_duration": "total_stt_audio_duration"
        }

        for key, value in matches:
            target_key = key_mapping.get(key)
            if target_key:
                usage[target_key] = float(value) if '.' in value else int(value)

        return usage

    def to_csv(self, output_path=None):
        """
        Convert JSON log to CSV with all required columns.
        """
        if not output_path:
            base_name = os.path.splitext(self.json_file_path)[0]
            output_path = f"{base_name}.csv"

        # Session info
        session_start = self._convert_to_ist(self.session_info.get("start_time"))
        session_end = self._convert_to_ist(self.session_info.get("end_time"))
        session_duration = f"{self.session_info.get('duration_seconds', 0):.2f}"

        # Count total assistant turns
        total_turns = len([t for t in self.conversation if "assistant_turn" in t])

        # Parse usage summary
        usage_data = self._parse_usage_summary()

        # Define CSV columns
        fieldnames = [
            "user_query",
            "bot_response",
            "associated_tool_call",
            "context",
            "stt_duration",
            "stt_audio_duration",
            "llm_duration",
            "llm_ttft",
            "llm_prompt_tokens",
            "llm_completion_tokens",
            "llm_tokens_per_second",
            "tts_ttfb",
            "tts_duration",
            "tts_audio_duration",
            "tts_characters_count",
            "tts_cancelled",
            "session_start",
            "session_end",
            "session_duration_seconds",
            "total_turns",
            "total_llm_prompt_tokens",
            "total_llm_prompt_cached_tokens",
            "total_llm_completion_tokens",
            "total_tts_characters_count",
            "total_tts_audio_duration",
            "total_stt_audio_duration"
        ]

        with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for turn in self.conversation:
                if "user_turn" in turn:
                    user_turn = turn["user_turn"]
                    assistant_turn = turn.get("assistant_turn", {})

                    row = {
                        "user_query": user_turn.get("content", ""),
                        "bot_response": assistant_turn.get("content", ""),
                        "associated_tool_call": self._extract_tool_calls(assistant_turn.get("function_calls", [])),
                        "context": self._extract_context(assistant_turn.get("function_calls", [])),
                        "session_start": session_start,
                        "session_end": session_end,
                        "session_duration_seconds": session_duration,
                        "total_turns": total_turns
                    }

                    # Update with usage data (same for all rows)
                    row.update(usage_data)

                    # Extract metrics
                    stt_metrics = user_turn.get("metrics", {}).get("stt", [])
                    llm_metrics = assistant_turn.get("metrics", {}).get("llm", [])
                    tts_metrics = assistant_turn.get("metrics", {}).get("tts", [])

                    row.update(self._extract_stt_metrics(stt_metrics))
                    row.update(self._extract_llm_metrics(llm_metrics))
                    row.update(self._extract_tts_metrics(tts_metrics))

                    writer.writerow(row)

        print(f"✅ CSV successfully saved to: {output_path}")
        return output_path


# --- Test the class ---
if __name__ == "__main__":
    input_file = "metrics/playground-BTpR-I7up_metrics_20250805_092604.json"

    if not os.path.exists(input_file):
        print(f"❌ File {input_file} not found!")
        exit(1)

    try:
        processor = PostProcess(input_file)
        processor.to_csv()
    except Exception as e:
        print(f"❌ Error processing file: {e}")
        raise