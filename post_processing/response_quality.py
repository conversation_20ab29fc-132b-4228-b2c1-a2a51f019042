"""
Conversation Quality Analyzer for P2P Lending Bot Interactions
Processes CSV data, evaluates bot responses via LLM, and writes scores back to the same file.
"""

import csv
import json
from pathlib import Path
from typing import Any, Dict, List, Optional

import asyncio

from llm_client import Async<PERSON><PERSON>lient
from prompts import check_response_quality_system_prompt, check_response_quality_user_prompt


class ConversationAnalyzer:
    """
    A robust analyzer for evaluating bot conversations in P2P lending domain.
    
    Reads conversation data from CSV, sends it for LLM-based scoring,
    and writes structured evaluation results back to the same CSV.
    """

    def __init__(self, csv_file_path: str, llm_client: AsyncLLMClient) -> None:
        """
        Initialize the analyzer.

        Args:
            csv_file_path: Path to the input/output CSV file (tab-separated).
            llm_client: Pre-configured async LLM client with JSON response support.
        """
        self.csv_file_path = Path(csv_file_path)
        self.llm_client = llm_client
        self.conversation_pairs: List[Dict[str, Any]] = []

    def parse_csv_to_json_structure(self) -> None:
        """
        Read the CSV and convert each row into a standardized conversation pair structure.
        
        Expects columns: user_query, bot_response, associated_tool_call, context
        """
        if not self.csv_file_path.exists():
            raise FileNotFoundError(f"Input CSV file not found: {self.csv_file_path}")

        with open(self.csv_file_path, mode="r", encoding="utf-8") as file:
            reader = csv.DictReader(file, delimiter="\t")  # Tab-separated
            self.conversation_pairs = []

            for index, row in enumerate(reader, start=1):
                self.conversation_pairs.append({
                    "id": index,
                    "user_query": row["user_query"].strip(),
                    "bot_response": row["bot_response"].strip(),
                    "function_call": row["associated_tool_call"].strip() if row.get("associated_tool_call") else None,
                    "context": row["context"].strip() if row.get("context") else None,
                })

    def format_transcription_for_prompt(self) -> str:
        """
        Serialize conversation data into a clean JSON string for inclusion in the LLM prompt.
        
        Returns:
            JSON string representation of conversation pairs.
        """
        return json.dumps(
            {"conversation_pairs": self.conversation_pairs},
            ensure_ascii=False,
            indent=2,
        )

    async def request_quality_analysis(self) -> Dict[str, Any]:
        """
        Send conversation data to LLM for multi-dimensional quality scoring.
        
        Uses JSON response format to ensure valid output structure.
        
        Returns:
            Parsed JSON response containing scores and notes.
        """
        system_prompt = check_response_quality_system_prompt()
        user_prompt_template = check_response_quality_user_prompt()[0]
        transcription_data = self.format_transcription_for_prompt()

        user_prompt = user_prompt_template.replace("{transcription_data}", transcription_data)

        response = await self.llm_client.simple_query(
            user_message=user_prompt,
            system_message=system_prompt,
            temperature=0.1,
            max_tokens=2048,
            response_format={"type": "json_object"},  # Enforce valid JSON
        )

        if not response:
            raise RuntimeError("LLM returned an empty or invalid response.")

        return json.loads(response)  # Guaranteed to be valid JSON

    def write_results_to_csv(self, analysis: Dict[str, Any]) -> None:
        """
        Write evaluation results back to the original CSV file.
        
        First row contains overall metrics, followed by individual pair scores.
        
        Args:
            analysis: LLM-generated analysis in expected JSON format.
        """
        fieldnames = [
            "pair_id",
            "user_query",
            "bot_response",
            "function_call_success",
            "context_relevance_score",
            "groundedness_score",
            "notes",
            "total_pairs",
            "function_calls_made",
            "function_call_success_rate",
        ]

        with open(self.csv_file_path, mode="w", encoding="utf-8", newline="") as file:
            writer = csv.DictWriter(file, fieldnames=fieldnames)
            writer.writeheader()

            # Write overall statistics in the first row
            overall = analysis["overall_analysis"]
            writer.writerow({
                "pair_id": "",
                "user_query": "",
                "bot_response": "",
                "function_call_success": "",
                "context_relevance_score": "",
                "groundedness_score": "",
                "notes": "OVERALL ANALYSIS",
                "total_pairs": overall["total_pairs"],
                "function_calls_made": overall["function_calls_made"],
                "function_call_success_rate": f"{overall['function_call_success_rate']}%",
            })

            # Write individual conversation pair evaluations
            for item in analysis["conversation_pairs"]:
                writer.writerow({
                    "pair_id": item["pair_id"],
                    "user_query": item["user_query"],
                    "bot_response": item["bot_response"],
                    "function_call_success": item["function_call_success"],
                    "context_relevance_score": item["context_relevance_score"],
                    "groundedness_score": item["groundedness_score"],
                    "notes": item.get("notes", ""),
                    "total_pairs": "",
                    "function_calls_made": "",
                    "function_call_success_rate": "",
                })

    async def analyze(self) -> None:
        """
        Execute the full analysis pipeline:
        1. Parse input CSV into structured format
        2. Request LLM-based quality scoring
        3. Write results back to the same CSV file
        """
        self.parse_csv_to_json_structure()
        analysis = await self.request_quality_analysis()
        self.write_results_to_csv(analysis)
        print(f"✅ Analysis completed and saved to {self.csv_file_path}")


async def main() -> None:
    """
    Entry point for standalone execution.
    """
    # Initialize LLM client with JSON response capability
    llm_client = AsyncLLMClient(
        model_name="llama-3.3-70b-versatile",
        temperature=0.1,
        max_tokens=2048,
    )

    # Configure analyzer with your data path
    analyzer = ConversationAnalyzer("metrics/playground-BTpR-I7up_metrics_20250805_092604.csv", llm_client)  # Replace with actual path if needed

    try:
        await analyzer.analyze()
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())